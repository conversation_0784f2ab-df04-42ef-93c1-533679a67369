<div style="display: flex; width: 100%; height: calc(100vh - 16px); flex: 1; position: relative;">
    <div style="display: flex; flex-direction: column; width: 100%; position: relative;">
        <div style="display: flex; width: 100%; color: gray;">
            <div style="width: 100%;">Source code</div>
            <div style="width: 100%;" hidden class="is-hidden">parseJS(Source code)</div>
            <div style="width: 100%;">joinJsTrees(Sourse + Extract)</div>
        </div>
        <div style="display: flex; height: 100%;">
            <div id="editor1" style="width: 100%; height: 96%; margin: 8px; border: 1px solid lightgray;"></div>
            <div id="editor12" style="width: 100%; height: 96%; margin: 8px; border: 1px solid lightgray;" hidden
                class="is-hidden"></div>
            <div id="editor3" style="width: 100%; height: calc(100% - 30x); margin: 8px; border: 1px solid lightgray;">
            </div>
        </div>
        <div style="display: flex; width: 100%; color: gray;">
            <div style="width: 100%;">Source code Editor</div>
            <div style="width: 100%;" hidden class="is-hidden">parseJS(Editor code) </div>
            <div style="width: 100%;">extractJS(Source + Editor)</div>
            <div style="width: 100%;" hidden class="is-hidden">parseJS(Extract code)</div>
        </div>
        <div style="display: flex; height: 100%; position: relative;">
            <div id="editor2" style="width: 100%; height: 96%; margin: 8px; border: 1px solid lightgray;"></div>
            <div id="editor22" style="width: 100%; height: 96%; margin: 8px; border: 1px solid lightgray;" hidden
                class="is-hidden"></div>
            <div id="editor4" style="width: 100%; height: calc(100% - 30x); margin: 8px; border: 1px solid lightgray;">
            </div>
            <div id="editor41" style="width: 100%; height: calc(100% - 30x); margin: 8px; border: 1px solid lightgray;"
                hidden class="is-hidden"></div>
        </div>
    </div>
</div>
<button id="btn" style="position: absolute; left: 160px; top: 50%; z-index: 999; color: red;"
    onclick="fn()">refresh</button>
<button id="btn2" style="position: absolute; left: 240px; top: 50%; z-index: 999; color: red;" onclick="fn2()">show /
    hide parse</button>


<script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.23.4/ace.js"></script>

<script type="module">
    const editor1 = ace.edit("editor1")
    editor1.setTheme("ace/theme/chrome")
    editor1.session.setMode("ace/mode/javascript")

    let first = await fetch('./_first.js');
    // first = await fetch('./_second.js');

    first = await first.text();
    editor1.setValue(first);

    editor1.clearSelection()
    const editor12 = ace.edit("editor12")
    editor12.setTheme("ace/theme/solarized_light")
    editor12.session.setMode("ace/mode/json")


    const editor2 = ace.edit("editor2")
    editor2.setTheme("ace/theme/chrome")
    editor2.session.setMode("ace/mode/javascript")

    let second = await fetch('./_second.js');
    // second = await fetch('./_first.js');
    second = await second.text();
    editor2.setValue(second);

    editor2.clearSelection();
    const editor22 = ace.edit("editor22")
    editor22.setTheme("ace/theme/solarized_light")
    editor22.session.setMode("ace/mode/json")

    const editor3 = ace.edit("editor3")
    editor3.setTheme("ace/theme/chrome")
    editor3.session.setMode("ace/mode/javascript")

    const editor4 = ace.edit("editor4")
    editor4.setTheme("ace/theme/chrome")
    editor4.session.setMode("ace/mode/javascript")

    const editor41 = ace.edit("editor41")
    editor41.setTheme("ace/theme/solarized_light")
    editor41.session.setMode("ace/mode/javascript")

    window.fn2 = () => {
        const hiddenElements = document.querySelectorAll('.is-hidden');
        hiddenElements.forEach(element => {
            if (element.hasAttribute('hidden')) {
                element.removeAttribute('hidden');
            } else {
                element.setAttribute('hidden', '');
            }
        })
    }
    window.fn = () => {

        function parseJS(src) {
            if (!src) return [];
            let pairs = { '[': ']', '{': '}', '(': ')', '`': '`' };
            let open_chars = Object.keys(pairs);
            let close_chars = Object.values(pairs);
            src = src.split(/\r\n|\r|\n/);
            let tree = [];
            let node = tree;
            for (let row of src) {
                let s = row.trim();
                let prev = node[Symbol.for('prev')];
                let open_char = pairs[prev?.last];
                if (s.startsWith(open_char) && s.replaceAll?.(' ', '').length > 2) {
                    let str = row.split(open_char)[0];
                    node.push(str + open_char);
                    node[Symbol.for('prev')] = null;
                    node = prev.prev;
                    row = row.replace(open_char, '')
                }
                let last = s.slice(-1);
                if (last === '`' && s.replaceAll?.(' ', '').length <= 2) last = '';
                node.push(row);
                if (open_chars.includes(last)) {
                    let prev = node;
                    node = [];
                    node[Symbol.for('prev')] = { prev, last };
                    prev.push(node)
                    continue;
                }
                prev = node[Symbol.for('prev')];
                if (!prev)
                    continue;
                let first = s.trim()[0];
                if (close_chars.includes(first)) {
                    if (pairs[prev.last] !== first)
                        continue;
                    node[Symbol.for('prev')] = null;
                    node = prev.prev;
                }
            }
            return tree;
        }

        function joinJsTrees(tree1, tree2, parentBlockKey) {
            let pairs = { '[': ']', '{': '}', '(': ')', '`': '`' };
            const checkComma = (key, item) => {
                if ((key?.endsWith(':{') || key?.endsWith('[') || key?.endsWith('default{')) && item?.length) {
                    item = item.map((i, idx) => {
                        if (Array.isArray(i)) {
                            let last = i.pop();
                            if (last.replaceAll?.(' ', '').at(-1) !== ',')
                                last += ',';
                            i.push(last);
                        } else if (i.replaceAll?.(' ', '') && !i?.endsWith('{') && !i?.endsWith('[')) {
                            let char = i.replaceAll?.(' ', '').at(-1);
                            // console.log(idx, pairs[parentBlockKey.at(-1)], str)
                            if (char && !pairs[char] && pairs[parentBlockKey.at(-1)] !== char && char !== ',' && char !== '`')
                                i += ',';
                        }
                        return i;
                    })
                }
                return item;
            }
            let res = [];
            let item1, idx2;
            while ((item1 = tree1.shift()) !== undefined) {
                if (typeof item1 === 'string') {
                    res.push(item1);
                    let key = item1.split(/\r\n|\r|\n/).join('').replaceAll?.(' ', '');
                    let last = key.at(-1);
                    if (pairs[last] && parentBlockKey?.at(-1) !== pairs[last])
                        parentBlockKey = key;
                    idx2 = tree2.findIndex(v2 => v2.replaceAll?.(' ', '') === key);
                    if (idx2 === -1) {
                        if (key.startsWith('return') && Array.isArray(tree2)) {
                            let isOk;
                            for (let i = 0; i < tree2.length; i++) {
                                const row = tree2[i];
                                if (row.replaceAll?.(' ', '').startsWith('return')) {
                                    res[res.length - 1] = row;
                                    tree2.splice(i, 1);
                                    isOk = true;
                                    break;
                                } 
                            }
                            if (isOk)
                                continue;
                        }
                        const ss = [':', '=', '(', '`', '[', '{', '>'];
                        let s, _key;
                        while ((s = ss.shift()) !== undefined) {
                            _key = key.split(s);
                            if (_key.length < 2)
                                continue;
                            break;
                        }
                        if (!s || _key.length < 2)
                            continue;
                        key = _key[0];
                        idx2 = tree2.findIndex(v2 => v2.replaceAll?.(' ', '')?.split(s)?.[0] === key);
                        if (idx2 === -1)
                            continue;
                        let newValue = tree2[idx2];
                        res[res.length - 1] = newValue;
                    }
                    tree2.splice(idx2, 1);
                }
                else if (Array.isArray(item1)) {
                    let item2 = tree2[idx2];
                    if (Array.isArray(item2)) {
                        tree2.splice(idx2, 1);
                        item1 = checkComma(parentBlockKey, item1);
                        item2 = checkComma(parentBlockKey, item2);
                        item1 = joinJsTrees(item1, item2, parentBlockKey);
                    }
                    const returnIndex = item1.findIndex(str => str.includes('return'));
                    if (returnIndex >= 0 && returnIndex < item1.length - 2) {
                        if (!item1[returnIndex].trim().endsWith('{')) {
                            const result = item1.filter((str, index) => index !== returnIndex);
                            result.splice(-1, 0, item1[returnIndex]);
                            item1 = result;
                        }
                    }
                    res.push(item1);
                    let last = item1?.at?.(-1);
                    if (Object.values(pairs).includes(last))
                        parentBlockKey = null;
                }
            }
            if (tree2.length) {
                if (parentBlockKey) {
                    let end = res.pop();
                    res.push(...tree2, end);
                }
                else {
                    res.push(...tree2);
                }
            }
            return res;
        }

        function extractJS(tree1, tree2, parentBlockKey) {
            let pairs = { '[': ']', '{': '}', '(': ')', '`': '`' };
            let res = [], block = [], item1, idx2, last;
            while ((item1 = tree1.shift()) !== undefined) {
                if (typeof item1 === 'string') {
                    let key = item1.split(/\r\n|\r|\n/).join('').replaceAll?.(' ', '');
                    if (!key) {
                        continue;
                    }
                    last = key.at(-1);
                    if (pairs[last]) {
                        block.push(item1);
                        parentBlockKey = key;
                    }
                    last = pairs[last];
                    // if (key.startsWith(last) && tree2.startsWith(last)) {
                    //     tree2.splice(idx2, 1);
                    // }
                    idx2 = tree2.findIndex(v2 => v2.replaceAll?.(' ', '') === key);
                    if (idx2 === -1) {
                        key = key.split(':');
                        if (!parentBlockKey || key.length < 2) {
                            continue;
                        }
                        key = key[0];
                        idx2 = tree2.findIndex(v2 => v2.replaceAll?.(' ', '')?.split(':')?.[0] === key);
                        if (idx2 === -1) {
                            continue;
                        }
                        let newValue = tree2[idx2];
                        if (newValue.replaceAll?.(' ', '').at(-1) !== ',')
                            newValue += ',';
                        res ||= [];
                        res.push(newValue);
                    }
                    tree2.splice(idx2, 1);
                }
                else if (Array.isArray(item1)) {
                    let item2 = tree2[idx2];
                    if (Array.isArray(item2)) {
                        tree2.splice(idx2, 1);

                        let isObj = parentBlockKey?.split(':{').length === 2;
                        let end1 = item1.at(-1);
                        let end2 = item2.at(-1);
                        if (end2.trim().length > end1.trim().length)
                            end1 = end2;
                        if (isObj) {
                            if (end1.replaceAll?.(' ', '').at(-1) !== ',')
                                end1 += ',';
                        }

                        let next = extractJS(item1, item2, parentBlockKey);
                        if (next.length) {
                            last = pairs[parentBlockKey.at(-1)];
                            res.push(block, next);
                            res.push(end1);
                        }
                        block = [];
                    }
                    else {
                    }
                }
            }

            if (tree2.length) {
                // console.log(tree2)
                tree2.map(i => {
                    if (typeof i === 'string') {
                        let _i = i?.replaceAll?.(' ', '');
                        if (_i)
                            res.push(i);

                    } else {
                        res.push(i);
                    }
                })
            }
            return res;
        }

        // PARSE Source code
        let tree1 = editor1.getValue();
        let parse1 = parseJS(tree1);
        editor12.setValue(JSON.stringify(parse1, undefined, 2));
        editor12.clearSelection();

        // PARSE Editor code
        let tree2 = editor2.getValue();
        let parse2 = parseJS(tree2);
        editor22.setValue(JSON.stringify(parse2, undefined, 2));
        editor22.clearSelection();


        // EXTRACT Editor code
        let resExtract = (extractJS(parse1, parse2) || []).flat(Infinity).join('\n');
        editor4.setValue(resExtract)
        editor4.clearSelection()

        // PARSE Extract code
        let parseExtract = parseJS(resExtract);
        editor41.setValue(JSON.stringify(parseExtract, undefined, 2));
        editor41.clearSelection();

        // MERGE Source + Extract
        parse1 = parseJS(tree1);
        let resMerge = joinJsTrees(parse1, parseExtract || []).flat(Infinity).join('\n');
        editor3.setValue(resMerge)
        editor3.clearSelection()

    }
    fn()
</script>