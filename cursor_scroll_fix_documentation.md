# Исправление прокрутки курсора в Jupyter редакторе

## Описание проблемы

Функция `on_change_cursor` в файле `web/oda/tools/jupyter/jupyter.js` имела проблему с автоматической прокруткой при быстром перемещении курсора (например, при зажатой клавише стрелки). 

### Симптомы:
- При медленном перемещении курсора прокрутка работала корректно
- При быстром перемещении (зажатая клавиша) прокрутка "застревала" 
- Курсор мог оказаться на строке 1, а видимой оставалась строка 7

### Причина проблемы:
Использование `debounce` для прокрутки вверх отменяло предыдущие вызовы, что приводило к пропуску промежуточных позиций прокрутки.

## Решение

### 1. Стратегия "дождаться и прокрутить один раз"
```javascript
// Новый подход: ждем окончания быстрого движения
this.debounce('cursor_scroll_final', () => {
    this._performFinalScroll();
}, 150); // Ждем 150ms после последнего движения курсора
```

### 2. Отдельная функция финальной прокрутки
```javascript
_performFinalScroll() {
    // Вычисляем точную позицию для прокрутки
    // Прокручиваем один раз до нужной позиции
    // Добавляем отступы для лучшей видимости
}
```

### 3. Адаптивная стратегия
- **Быстрое движение**: накапливаем изменения, прокручиваем один раз в конце
- **Медленное движение** (>300ms между изменениями): прокрутка выполняется немедленно

### 4. Улучшенная логика прокрутки
- Точное вычисление целевой позиции
- Добавление отступов для лучшей видимости (2-3 строки)
- Проверка границ для предотвращения выхода за пределы

## Технические детали

### Принцип работы debounce:
- **debounce**: откладывает выполнение функции до окончания серии вызовов
- Идеально подходит для "финальной" прокрутки после завершения движения

### Задержка debounce:
- 150ms - оптимальная задержка для определения окончания быстрого движения
- Достаточно быстро для отзывчивости, но позволяет завершить серию движений

### Оптимизация производительности:
- Один вызов прокрутки вместо множества промежуточных
- Проверка изменения строки курсора предотвращает ненужные вычисления
- Адаптивная стратегия: немедленная прокрутка для медленного движения (>300ms)

## Тестирование

### Сценарии для проверки:
1. **Медленное движение**: нажимайте стрелки с паузами - прокрутка должна быть мгновенной
2. **Быстрое движение**: зажмите стрелку - прокрутка должна быть плавной без застреваний
3. **Смешанное движение**: чередуйте быстрое и медленное движение
4. **Большие файлы**: тестируйте на файлах с сотнями строк

### Ожидаемый результат:
- Курсор всегда остается в видимой области
- Нет "застреваний" при быстром движении
- Плавная прокрутка без рывков
- Хорошая производительность

## Файлы изменений

- `web/oda/tools/jupyter/jupyter.js` - основные изменения в функции `on_change_cursor`
- `test_cursor_scroll.html` - тестовая страница для проверки
- `cursor_scroll_fix_documentation.md` - данная документация

## Совместимость

Изменения совместимы с существующим кодом и не влияют на другие функции редактора.
