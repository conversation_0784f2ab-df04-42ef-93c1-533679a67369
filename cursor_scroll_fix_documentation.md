# Исправление прокрутки курсора в Jupyter редакторе

## Описание проблемы

Функция `on_change_cursor` в файле `web/oda/tools/jupyter/jupyter.js` имела проблему с автоматической прокруткой при быстром перемещении курсора (например, при зажатой клавише стрелки). 

### Симптомы:
- При медленном перемещении курсора прокрутка работала корректно
- При быстром перемещении (зажатая клавиша) прокрутка "застревала" 
- Курсор мог оказаться на строке 1, а видимой оставалась строка 7

### Причина проблемы:
Использование `debounce` для прокрутки вверх отменяло предыдущие вызовы, что приводило к пропуску промежуточных позиций прокрутки.

## Решение

### 1. Замена debounce на throttle
```javascript
// Было:
this.debounce('jupyter.scrollTop', ()=>{
    this.jupyter.scrollTop = move;
})

// Стало:
this.throttle('jupyter.scrollTop.up', ()=>{
    this.jupyter.scrollTop = move;
}, 16) // ~60fps для плавности
```

### 2. Добавление определения скорости движения
```javascript
let now = Date.now();
this._lastCursorMoveTime = this._lastCursorMoveTime || 0;
let timeDiff = now - this._lastCursorMoveTime;
this._lastCursorMoveTime = now;

// Определяем, быстрое ли это движение (менее 100ms между изменениями)
let isFastMovement = timeDiff < 100;
```

### 3. Адаптивная стратегия прокрутки
- **Быстрое движение**: используется `throttle` с частотой 60fps (16ms)
- **Медленное движение**: прокрутка выполняется немедленно

### 4. Улучшенная проверка видимости
- Более точное определение границ видимой области
- Отдельные ключи throttle для движения вверх и вниз
- Проверка на изменение строки курсора

## Технические детали

### Разница между debounce и throttle:
- **debounce**: откладывает выполнение функции до окончания серии вызовов
- **throttle**: выполняет функцию регулярно с заданной частотой

### Частота обновления:
- 16ms = ~60 FPS для плавной анимации
- Соответствует частоте обновления большинства мониторов

### Оптимизация производительности:
- Проверка изменения строки курсора предотвращает ненужные вычисления
- Адаптивная стратегия снижает нагрузку при медленном движении

## Тестирование

### Сценарии для проверки:
1. **Медленное движение**: нажимайте стрелки с паузами - прокрутка должна быть мгновенной
2. **Быстрое движение**: зажмите стрелку - прокрутка должна быть плавной без застреваний
3. **Смешанное движение**: чередуйте быстрое и медленное движение
4. **Большие файлы**: тестируйте на файлах с сотнями строк

### Ожидаемый результат:
- Курсор всегда остается в видимой области
- Нет "застреваний" при быстром движении
- Плавная прокрутка без рывков
- Хорошая производительность

## Файлы изменений

- `web/oda/tools/jupyter/jupyter.js` - основные изменения в функции `on_change_cursor`
- `test_cursor_scroll.html` - тестовая страница для проверки
- `cursor_scroll_fix_documentation.md` - данная документация

## Совместимость

Изменения совместимы с существующим кодом и не влияют на другие функции редактора.
