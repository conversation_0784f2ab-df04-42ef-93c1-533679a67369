<!DOCTYPE html>
<meta charset="utf-8">

<oda-merge id="oda_merge"></oda-merge>

<script type="module">
    import '/$oda/oda.js';
    import './merge.js';

    /// oda_diff.fullMode = true;

    const code = `
export default {
    subIcon: 'fontawesome:s-gears',
    icon: 'icons:create',
    allowAccess: 'A',
    quickTool: true,
    aa: {
        get() {
            return 100;
        }
    },
    async execute(params) {
        return this.$context.$folder.then(f => f.open('files', '', this.contextItem.filePath));
    },
}
    
class MyClass extends Reactor {
    constructor() {
        super();
    }
    get $public() {
        return{
            get a() {
                return 100;
            }
        }
    }
    get b() {
        return this.a * 2;
    }
}

function showMessage() {
    alert( 'Всем привет!' );
}
  
`
    oda_merge.source = oda_merge.edit = code;
</script>
