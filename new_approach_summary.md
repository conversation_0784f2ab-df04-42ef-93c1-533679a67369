# Новый подход к исправлению прокрутки курсора

## 🎯 Основная идея

**"Дождаться окончания быстрого движения и прокрутить один раз точно"**

Вместо попыток подматывать во время движения, мы ждем, пока пользователь закончит быстрое перемещение курсора, и только тогда выполняем одну точную прокрутку до нужной позиции.

## 🔧 Как это работает

### 1. Обнаружение движения курсора
```javascript
// Сохраняем позицию для финальной прокрутки
this._pendingScrollRow = currentRow;
```

### 2. Debounce для определения окончания движения
```javascript
this.debounce('cursor_scroll_final', () => {
    this._performFinalScroll();
}, 150); // Ждем 150ms после последнего движения
```

### 3. Финальная точная прокрутка
```javascript
_performFinalScroll() {
    // Вычисляем точную позицию
    // Добавляем отступы для комфорта
    // Прокручиваем один раз
}
```

## ✅ Преимущества нового подхода

1. **Нет "застреваний"** - прокрутка всегда доходит до финальной позиции
2. **Высокая производительность** - минимум вызовов прокрутки
3. **Точность** - курсор всегда оказывается в видимой области
4. **Комфорт** - добавляются отступы для лучшей видимости
5. **Адаптивность** - медленное движение обрабатывается мгновенно

## 🎮 Поведение для пользователя

### При зажатой клавише (быстрое движение):
1. Курсор быстро перемещается по строкам
2. Прокрутка НЕ происходит во время движения
3. Через 150ms после отпускания клавиши - одна точная прокрутка
4. Курсор оказывается в видимой области с отступами

### При медленном движении (>300ms между нажатиями):
1. Каждое движение курсора обрабатывается мгновенно
2. Прокрутка происходит сразу
3. Никаких задержек

## 🔍 Технические детали

- **Задержка debounce**: 150ms (оптимально для определения окончания движения)
- **Порог медленного движения**: 300ms между изменениями
- **Отступы**: 2-3 строки для комфортной видимости
- **Проверки**: только при реальном изменении строки курсора

## 🧪 Результат тестирования

Этот подход должен полностью решить проблему "застревания" прокрутки, так как:
- Мы не пытаемся угнаться за быстрым движением
- Ждем окончания и делаем одну точную прокрутку
- Всегда доходим до финальной позиции курсора
