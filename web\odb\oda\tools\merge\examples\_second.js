export default {
    subIcon: 'fontawesome:s-gears',
    icon: 'icons:create',
    allowAccess: 'A',
    quickTool: true,
    aa: {
        get() {
            return 100;
        }
    },
    async execute(params) {
        return this.$context.$folder.then(f => f.open('files', '', this.contextItem.filePath));
    },
    template: `
        <h1>TEST</h1>
    `
}
    
class MyClass extends Reactor {
    constructor() {
        super();
    }
    get $public() {
        return{
            get a() {
                let a = 40;
                let b = 60;
                return a + b;
            }
        }
    }
    get b() {
        return this.a * 2;
    }
}

function showMessage() {
    alert( 'Всем привет!' );
}

function sum(a, b) {
    let res = a + b;
    return res;
}