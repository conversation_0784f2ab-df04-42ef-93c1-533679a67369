ODA({
    is: 'oda-merge', imports: '@oda/code-editor, @oda/splitter, @oda/button',
    template: `
        <style>
            :host {
                @apply --vertical;
                @apply --flex;
                overflow: hidden;
                position: relative;
                height: 100vh;
            }
            .info {
                position: absolute;
                z-index: 999;
                opacity: 0.5;
                font-size: small;
                padding-right: 4px;
            }
        </style>
        <div class="horizontal flex">
            <oda-code-editor id="ace-source" class="vertical flex" :src="source" @change="changeEditor" wrap @loaded="setupSourceEditor" style="border-right: 1px solid gray; height: 100%;"></oda-code-editor>
            <oda-splitter vertical @resize="resize" size="4"></oda-splitter>
            <oda-code-editor id="ace-editor" class="vertical flex" :src="edit" wrap style="height: 100%;" @change="changeEditor" @loaded="setupEditor"></oda-code-editor>
        </div>
        <oda-splitter horizontal @resize="resize" size="4"></oda-splitter>
        <oda-code-editor id="ace-output" class="vertical flex" :src="output" wrap @loaded="setupOutputEditor" style="border-top: 1px solid gray;"></oda-code-editor>
        `,
    source: '',
    output: '',
    edit: '',
    lowOpacity: .3,
    setupEditor(e) {
        this.editor = e.target.editor;
    },
    setupSourceEditor(e) {
        this.sourceEditor = e.target.editor;
    },
    setupOutputEditor(e) {
        this.outputEditor = e.target.editor;
    },
    changeEditor() {
        const sourceText = this.sourceEditor.getValue();
        let editText = this.editor.getValue();
    
        let mergeed = sourceText.merge(editText);
        this.outputEditor.setValue(mergeed);
        this.outputEditor.clearSelection();
    },
    resize(e) {
        this.sourceEditor?.resize();
        this.editor?.resize();
        this.outputEditor?.resize();
    }
})
