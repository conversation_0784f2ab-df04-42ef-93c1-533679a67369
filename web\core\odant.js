import * as Auth from '../auth/auth.js';
const NO_CONTEXT_ALIASES = ['@oda', '@tools'];

if (!globalThis.ODANT) {
    try {
        await import('./core.js');
        await import("../oda/oda.js");
        await import('../oda/tools/localization/localization.js');
        // (() => {
        //     const script = document.createElement('script');
        //     script.src = CORE.URL.origin + '/web/oda/components/editors/xml-editor/sax-min.js';
        //     document.head.appendChild(script);
        // })()
        if (ODA.localization) {
            ODA.localization.updateDictionary([CORE.host.url + '/P:ROOT/~/settings/localization/dictionary/'])
            ODA.localization.saveMethod = async (obj) => {
                console.log('localization saveMethod')
                if (CORE.host.access.level < 6) return;
                const $root = await CORE.host.$root;
                await $root.saveFile(obj.structure, 'settings/localization/dictionary/structure.json');
                await $root.saveFile(obj.phrases, 'settings/localization/dictionary/phrases.json');
                await $root.saveFile(obj.dictionary, 'settings/localization/dictionary/' + obj.lang + '.json');
            }
        }
        initODANTStyles();
        overrideODAFunctions();

        await initUserInfo();
        await initODANT();

        globalThis.ODANT.IsReady = true;
        window.dispatchEvent(new Event('odant-ready'));
    }
    catch (err) {
        console.error(err);
    }
}

export default globalThis.ODANT;


function initODANTStyles() {
    const odant_style = /*css*/`
:root {
    --inherit-owner-color: orangered;
    --inherit-link-color: blue;
    --inherit-parent-color: blue;
    --inherit-type-color: green;
    --inherit-core-color: gray;
    --inherit-self-color: light-dark(black, white);

    --access-admin-color: light-dark(black, white);
    --access-rwcd-color: red;
    --access-rwc-color: green;
    --access-rw-color: blue;
    --access-r-color: gray;
    --access-p-color: silver;
    --access-none-color: light-dark(black, white);

    --item-color: light-dark(black, white);
    --item-class-color: orangered;
    --item-link-class-color: orangered;
    --item-lost-link-class-color: red;
    --item-module-class-color: orangered;
    --item-domain-color: light-dark(black, white);
    --item-host-color: light-dark(black, white);
    --item-part-color: light-dark(black, white);
    --item-base-color: brown;
    --item-module-color: magenta;
    --item-workplace-color: steelblue;
    --item-folder-color: orange;
    --item-file-color: light-dark(black, white);
    --item-field-color: light-dark(black, white);
    --item-abstract-field-color: silver;
}
`;
    if ('adoptedStyleSheets' in Document.prototype) {
        let ss = new CSSStyleSheet();
        ss.replaceSync(odant_style);
        globalThis.adopted.push(ss)
    }
    const rootStyle = document.createElement('style');
    rootStyle.setAttribute('scope', 'ODANT');

    rootStyle.textContent = odant_style;
    document.head.insertBefore(rootStyle, document.head.querySelectorAll('style')[0]);
}
function overrideODAFunctions() {
    const _createElement = ODA.createElement;
    ODA.import = function (url, prototype) {
        url = contextToURL(url, undefined, prototype);
        return ODA.cache[url] ??= import(url);
    }
    ODA.createElement = ODA.createComponent = function (id, props) {
        id = id.toLowerCase();
        const context = props?.contextItem || this.contextItem;
        if (id.includes('-') && context) {
            tryReg(id, context.path)
        }
        return _createElement(id, props);
    }
}
function regOdantContextComponent() {
    ODA({
        is: 'odant-context',
        $pdp: {
            iconSize: 24
        },
        $public: {
            focusedItem: {
                $pdp: true,
                $type: Object,
            },
            default: 'icons:error',
            context: {
                $pdp: true,
                get() {
                    return this.contextItem?.path
                },
                set(n) {
                    if (!n || !this.contextItem) {
                        this.default = ODANT.waitIcon;
                    }
                    if (n) {
                        this.default = 'odant:spin';
                        const result = CORE.host.findItem(n);
                        if (result?.then) {
                            result.then(res => {
                                this.contextItem = res;
                            }).catch(e => {
                                this.contextItem = null;
                                this.fill = 'red';
                                console.error(e)
                            })
                            this.contextItem = null;
                        }
                        else {
                            this.contextItem = result;
                        }
                    }
                },
            },
            contextItem: {
                $type: Object,
                $pdp: true
            }
        },
        /** @this {odantComponent} */
        get isChanged() {
            return this.contextItem?.isChanged;
        },
        /** @this {odantComponent} */
        async save() {
            if (!this.contextItem?.isChanged) return;
            return this.contextItem.save();
        },
        /** @this {odantComponent} */
        get icon() {
            return this.contextItem?.icon
        },
        onContextItemUpdated(e) {
            // console.log(e);
        },
        onContextItemChanged(e) {
            // console.log(e);
        },
        $observers: {
            setSaveKey(contextItem) {
                if (!contextItem) {
                    this.$saveKey = '';
                    this.default = 'icons:error';
                    this.fill = '';
                    this.stroke = '';
                    return;
                }
                this.listen('changed', 'onContextItemChanged', { target: contextItem });
                this.listen('update', 'onContextItemUpdated', { target: contextItem });
                if (this.parentElement === document.body) {
                    contextItem.listen('delete', (e) => {
                        this.fire('form-close');
                        window.close();
                    });
                }
                // this.default = ODANT.waitIcon;

                const keyItem = contextItem.$class || contextItem;
                let key = keyItem?.path?.replace('/', '');
                if (key)
                    key += '/type=' + contextItem?.typeName;
                this.$saveKey ||= key;

                this.async(() => {
                    this.default = 'icons:error';
                }, 100);
            }
        }
    });
}
async function initODANT() {
    /**@param {ComponentPrototype} prototype */
    globalThis.ODANT = async function (prototype) {
        if (ODA.telemetry.components[prototype.is]) return ODA.telemetry.components[prototype.is]
        if (ODANT.deferred[prototype.is]?.promise) return ODANT.deferred[prototype.is]?.promise
        if (!prototype?.is) {
            let stack = (new Error()).stack;
            if (!stack) throw new Error("couldn't get the context");
            let path = stack.split('/client/pages/')?.[1];
            path = path.substring(0, path.lastIndexOf('/'));
            path = path.replaceAll('/', '-');
            const matches = stack.match(/(?<url>https?:\/\/(?:[^\/]*\/)*(?:(?<type>[^\/]+)\/)(?<side>(?:client)|(?:server))\/(?<category>[^\/]+)\/(?<name>[^\/]+)(?:\/(?:[^\/]+))*\/(?<fileName>(?:[^\/]+)\.js))/)?.groups || {};
            prototype.is = `odant-${path}`;
            prototype.$$handlerName = matches.name;
            const item = await extractContextItem(stack);
            if (item) {
                prototype.is += `-${item.id.toLowerCase()}`;
            }
        }
        prototype.imports = str2arr(prototype.imports);
        prototype.extends = str2arr(prototype.extends);
        if (!prototype.extends.some(e => e.includes('odant'))) {
            prototype.extends.unshift('odant-context');
        }
        if (prototype.imports.length) {
            return resolveImports(prototype)
        }
        else if (prototype.extends.filter(ext => ext !== 'this').length) {
            return resolveExtends(prototype)
        }
        return ODA(prototype)
    }
    ODA.import('./tools/containers.js');
    ODA.import('./field/field.js');
    regOdantContextComponent();

    ODANT.waitIcon = 'odant:spin';
    ODANT.origin = !location.host || location.origin.match(/https?:\/\/localhost/) ? 'https://current.odant.org' : location.origin;
    ODANT._fieldViews = {
        'field-input': [],
        'field-ext': [],
        'field-table': [],
        'field-toolbar': [],
    };
    ODANT.deferred = {};
    ODANT.modules = {}
    ODANT.waitReg = waitReg;
    ODANT.tryReg = tryReg;
    ODANT.createComponent = ODA.createComponent;
    globalThis.MAIN = async (prototype) => {
        let stack = (new Error()).stack;
        if (!stack) return;
        prototype.autoSave ??= {
            $type: Number,
            $public: true,
            $save: true,
            $def: 0
        };
        const matches = stack.match(/(?<url>https?:\/\/(?:[^\/()]*\/)*(?:(?<type>[^\/]+)\/)(?<side>(?:client)|(?:server))\/(?<category>[^\/]+)\/(?<name>[^\/]+)(?:\/(?:[^\/]+))*\/(?<fileName>(?:[^\/]+)\.js))/)?.groups || {};

        const $system = Object.create(null);
        $system.url = matches.url;
        $system.dir = $system.url.substring(0, $system.url.lastIndexOf('/')) + '/';
        if (prototype.$system) {
            prototype.$system.url = $system.url;
            prototype.$system.dir = $system.dir;
        }
        else {
            prototype.$system = $system;
        }

        const item = await extractContextItem(matches.url);
        if (item) {
            prototype.is = `odant-${matches.name}-${matches.type}-${item.id.toLowerCase()}`;
            prototype.$$handlerName = matches.name;
        }
        else {
            prototype.is = `odant-${matches.name}`;
            prototype.$$handlerName = matches.name;
        }
        return ODANT(prototype);
    }

    globalThis.VIEW = globalThis.MAIN;
    globalThis.PAGE = globalThis.MAIN;

    globalThis.FIELD_INPUT = (prototype) => registerField(prototype, 'field-input');
    globalThis.FIELD_TABLE = (prototype) => registerField(prototype, 'field-table');
    globalThis.FIELD_CONTROL = (prototype) => registerField(prototype, 'field-control');
    globalThis.FIELD_TOOLBAR = (prototype) => registerField(prototype, 'field-toolbar');
    globalThis.HEADER = (prototype) => { }// registerField(prototype, 'field');
    globalThis.FOOTER = (prototype) => { }// registerField(prototype, 'field');

    ODANT.showWelcome = async function (force = false, contextItem) {
        const context = CORE.getContextFromUrl(location.pathname);
        if (!contextItem) {
            try {
                contextItem ??= context ? await CORE.host.findItem(context) : CORE.host;
            }
            catch (err) {
                console.warn(err);
            }
        }
        if (
            !contextItem
            || contextItem.available === false
        ) {
            throw new Error('Context item is not available!');
        }
        if (force) {
            await CORE.host.executeHandler('authorization');
        }
    };

    ODANT.currentUnavailableDialog = null;
    ODANT.showUnavailableDialog = async function (contextItem = CORE.host) {
        if (ODANT.currentUnavailableDialog || ODA.top?.ODANT.currentUnavailableDialog) return;
        await ODA.top.CORE.host.import('@lib/unavailable-dialog');
        await ODA.top.ODA.waitReg('odant-unavailable-dialog');
        const ctrl = ODA.top.ODANT.createComponent('odant-unavailable-dialog', { contextItem: CORE.host });
        ODANT.currentUnavailableDialog = ctrl;
        await ctrl.show?.(contextItem).catch(() => {
            Auth.setSecurity({ login: 'guest' });
        });
        ODANT.currentUnavailableDialog = null;
    };

    Object.defineProperty(ODANT, 'language', { //todo: реактивное свойство environment ?
        get() {
            return ODA.language;
        },
        set(v) {
            ODA.language = v;
            (window.top || window).dispatchEvent?.(new Event('language-changed'));
        }
    });

    ODANT.show = async function (item, view, params = {}, dialog = false) {
        view = view || item.defaultView;
        /** @type {odantForm} */
        const form = await ODA.createComponent('odant-form', params);
        form.viewId = view;
        form.contextItem = item;
        return form.show(dialog);
    };
    const welcomeUser = async () => {
        let userDraft = null;
        try {
            let security = Auth.getSecurity();
            await ODANT.showWelcome(!security?.login);
            security = Auth.getSecurity();
            userDraft = {
                name: security.login,
                id: security.id
            };
        }
        catch (err) {
            const context = CORE.getContextFromUrl(location.pathname);
            await ODANT.showUnavailableDialog(context);
        }
        finally {
            userDraft ??= {
                name: 'guest',
            };
            await CORE.initUser(userDraft);
        }
    }
    await new Promise(async resolve => {
        if (globalThis.CORE?.coreIsReady) {
            await welcomeUser();
            resolve(0);
        }
        else {
            globalThis.addEventListener('core-ready', async () => {
                await welcomeUser();
                resolve(0);
            }, { once: true });
        }
    });
}








// #####################################################################################################################################################
// ##################################################################### functions #####################################################################
// #####################################################################################################################################################

function tryReg(name, context) {
    name = name.trim()
    const res = ODANT.deferred[name];
    if (ODA.telemetry.components[name]) {
        delete ODANT.deferred[name]
    }
    else if (typeof res?.reg === 'function') {
        res.reg(context);
    }
    else {
        ODANT.deferred[name] = { context };
    }
}
function waitReg(tag, context) {
    let res = ODANT.deferred[tag];
    if (res) {
        if (res.reg) {
            if (context) {
                res.reg(context);
                return ODA.telemetry.prototypes[tag]?.is;
            }
        }
        else if (context) {
            res ??= { context };
        }
        res = ODA.telemetry.prototypes[tag]?.is;
        if (res) {
            return (async () => {
                await res;
                return ODA.telemetry.components[tag]?.is;
            })();
        }
    }
    return ODA.waitDependence(tag);
}
function resolveImports(prototype) {
    if (prototype.imports.some(i => NO_CONTEXT_ALIASES.every(n => !i.startsWith(n)))) {
        if (ODANT.deferred[prototype.is]?.context) {
            contextifyImports(prototype, ODANT.deferred[prototype.is].context)
            contextifyExtends(prototype, ODANT.deferred[prototype.is].context)
            delete ODANT.deferred[prototype.is]
            return ODA(prototype)
        }
        else {
            ODANT.deferred[prototype.is] = {}
            ODANT.deferred[prototype.is].promise = new Promise(resolve => {
                ODANT.deferred[prototype.is].reg = (context) => {
                    contextifyImports(prototype, context)
                    contextifyExtends(prototype, context)
                    delete ODANT.deferred[prototype.is]
                    resolve(ODA(prototype))
                }
            })
            return ODANT.deferred[prototype.is].promise
        }
    }
    return ODA(prototype)
}
function contextToURL(url, context, prototype) {
    url = url.trim();
    if (url.startsWith('@') || url.startsWith('../@')) {
        if (!NO_CONTEXT_ALIASES.some(i => url.startsWith(i)) && context)
            url = ODANT.origin + '/api/' + context + '/' + url;
        else
            url = ODANT.origin + '/api/' + url;
    }
    else if (prototype) {
        if (url.startsWith('./'))
            url = prototype.$system.dir + url.substring(1);
        else if (url.startsWith('../'))
            url = prototype.$system.dir + '/' + url;
    }
    url = url.replace(/\/\//g, '/');
    return url;
}
function contextifyExtends(prototype, context) {
    prototype.extends.filter(ext => ext !== 'this').forEach(ext => tryReg(ext.name || ext, context))
}

function contextifyImports(prototype, context) {
    prototype.imports = prototype.imports.map(url => contextToURL(url, context, prototype))
}

function resolveExtends(prototype) {
    if (ODANT.deferred[prototype.is]?.context) {
        contextifyExtends(prototype, ODANT.deferred[prototype.is]?.context);
        delete ODANT.deferred[prototype.is]
        return ODA(prototype)
    }
    else {
        return new Promise(resolve => {
            ODANT.deferred[prototype.is] = {}
            ODANT.deferred[prototype.is].reg = (context) => {
                contextifyExtends(prototype, context);
                resolve(ODA(prototype))
            }
        })
    }
}
async function extractContextItem(stack) {
    const itemPath = getItemPath(stack);
    return CORE.host.findItem(itemPath);
}
/**@param {string=} stack */
function getItemPath(stack) {
    if (!stack) return undefined;
    stack = decodeURIComponent((stack || (new Error()).stack?.trim() || ''));
    const rows = stack.split('\n');

    for (let i = rows.length - 1; i >= 0; --i) {
        const row = rows[i];
        const itemPath = row.match(/\/api((\/[HDPMBCW]:([\d\w]*))*)/i)?.[1];
        if (itemPath)
            return itemPath;
    }
}
function registerField(prototype, type = 'FIELD') {
    type = type.toLowerCase();
    if (Object.keys(prototype).length === 0) {
        throw new Error('Empty prototype!!!');
    }
    const stack = (new Error()).stack;
    const itemPath = getItemPath(stack);
    // if (!itemPath) throw new Error("the field registration context could not be found");
    // if (!itemPath) return;
    globalThis.CORE.deferredFields[itemPath] ??= [];
    const prom = new Promise(async (resolve) => {
        prototype.extends = str2arr(prototype.extends);
        if (!prototype.extends.some(e => e.includes('odant-field'))) {
            prototype.extends.unshift('odant-field');
        }
        prototype.hostAttributes = prototype.hostAttributes || {};
        const cls = itemPath ? await CORE.host.findItem(itemPath) : null;

        if (cls && cls.prefix === 'C') {
            if (!prototype.is) {
                prototype.is = `odant-${type}` + '-' + (cls._fieldViews[type]?.length + 1);
            }
            prototype.hostAttributes['field-type'] = cls.label;
            prototype.is = `${prototype.is}-${cls.id.toLowerCase()}`;
            cls._fieldViews[type].push(prototype.is);
        }
        else {
            prototype.is = prototype.is || `odant-${type}`;
            ODANT._fieldViews[type].add(prototype.is);
        }
        ODANT(prototype)
        resolve(undefined);
    })
    globalThis.CORE.deferredFields[itemPath].push(prom)
    return prom;
}
async function initUserInfo() {
    if (window === window.top) {
        if (window.ODANTUserInfo) return;
        window.ODANTUserInfo = updateAuth();
    }
    // обновление SSID по расписанию
    Promise.resolve(ODA.top.ODANTUserInfo).then(res => {
        setInterval(updateAuth, 15 * 60 * 1000);
    });
}
async function updateAuth() {
    /**@type {{name: string, id?: string}} */
    const userDraft = {
        name: 'guest',
    };
    const security = Auth.getSecurity();
    if (security.login) {
        try {
            await Auth.checkSsid();
            userDraft.name = security.login;
            userDraft.id = security.id;
        }
        catch (err) {
            console.warn(err);
            switch (security.version) {
                case 3: {
                    Auth.goToRefreshAccessToken();
                } break;
                case 2:
                default: {
                    try {
                        const res = await Auth.authorize(security.token || security.code);
                        const uid = res?.result || res;
                        userDraft.name = security.login;
                        userDraft.id = uid;
                    }
                    catch (err) {
                        console.error(err);
                    }
                } break;
            }
        }
    }
    return userDraft;
}