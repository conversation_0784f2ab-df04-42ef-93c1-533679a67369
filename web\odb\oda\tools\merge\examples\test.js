export default {

   id: "nakladnaya",
   name: "nakladnaya",
   label: "Накладная",
  METADATA:{
    FIELDS:[
      // Add fields and sub-fields,
      // FIELD:{id: 'second-field'}
         { id: "number", label: "Номер", type: "string" },
         { id: "date", label: "Дата", type: "date" },
         { id: "supplier", label: "Поставщик", type: "string" },
         { id: "recipient", label: "Получатель", type: "string" },
         { id: "total_sum", label: "Итоговая сумма", type: "number", calculated: true },
         { id: "total_discount", label: "Общая скидка", type: "number", calculated: true },
         { id: "total_with_discount", label: "Итого со скидкой", type: "number", calculated: true },
         { id: "vat_rate", label: "Ставка НДС %", type: "number", default: 20 },
         { id: "vat_sum", label: "Сумма НДС", type: "number", calculated: true, expression: "total_with_discount * (vat_rate div 100)" },
         { id: "total_with_vat", label: "Всего с НДС", type: "number", calculated: true, expression: "total_with_discount + vat_sum" },
         { id: "items", label: "Товары", table: true,
            FIELDS: [
               { id: "name", label: "Наименование", type: "string" },
               { id: "quantity", label: "Количество", type: "number" },
               { id: "unit", label: "Единица измерения", type: "string" },
               { id: "price", label: "Цена", type: "number" },
               { id: "sum", label: "Сумма", type: "number", calculated: true, expression: "quantity * price" },
               { id: "discount", label: "Скидка %", type: "number" },
               { id: "sum_with_discount", label: "Сумма со скидкой", type: "number", calculated: true, expression: "sum * (1 - discount div 100)" }
            ]
         },
         { id: "notes", label: "Примечания", type: "string" },
         { id: "status", label: "Статус", type: "string" }
    ],
    STATIC:[
      // Add static fields and sub-fields,
         { id: "statuses", value: ["Черновик", "Выставлена", "Оплачена", "Отменена"] }
     ],
    INDEXES:[
      // Add indexes and sub-indexes,
      {id: 'table'},
         { id: "number" },
         { id: "date" },
         { id: "supplier" },
         { id: "recipient" },
         { id: "status" }
    ],
    HANDLERS:{
      // this part for handlers settings
         calculateTotal: true,
         validateItems: true
    },
   },
   create_time: new Date().toISOString(),

   // Методы для работы с накладной
   calculateTotal() {
      if (!this.items || !this.items.length) return 0;

      let total = 0;
      let totalDiscount = 0;

      for (const item of this.items) {
         // Рассчитываем сумму для каждой позиции
         if (item.quantity && item.price) {
            item.sum = item.quantity * item.price;
         }

         // Рассчитываем сумму со скидкой для каждой позиции
         if (item.sum && item.discount) {
            item.sum_with_discount = item.sum * (1 - item.discount / 100); // В коде используем JavaScript-синтаксис
         }
          else {
            item.sum_with_discount = item.sum;
         }

         total += item.sum || 0;
         totalDiscount += (item.sum || 0) - (item.sum_with_discount || 0);
      }

      this.total_sum = total;
      this.total_discount = totalDiscount;
      this.total_with_discount = total - totalDiscount;

      // Рассчитываем НДС и итоговую сумму с НДС
      if (this.vat_rate) {
         this.vat_sum = this.total_with_discount * (this.vat_rate / 100); // В коде используем JavaScript-синтаксис
         this.total_with_vat = this.total_with_discount + this.vat_sum;
      }

      return total;
   },

   validateItems() {
      if (!this.items || !this.items.length) {
         return { valid: false, message: "Накладная должна содержать хотя бы один товар" };
      }

      for (const item of this.items) {
         if (!item.name || !item.quantity || !item.price) {
            return {
               valid: false,
               message: "Все товары должны иметь наименование, количество и цену"
            };
         }
      }

      return { valid: true };
   },

   // Геттеры и сеттеры
   get formattedDate() {
      if (!this.date) return "";
      const date = new Date(this.date);
      return date.toLocaleDateString();
   },

   get isEditable() {
      return this.status === "Черновик";
   },

   // Методы для изменения статуса
   setStatus(newStatus) {
      if (this.METADATA.STATIC[0].value.includes(newStatus)) {
         this.status = newStatus;
         return true;
      }
      return false;
   },

   // Метод для добавления товара
   addItem(item) {
      if (!this.items) this.items = [];

      // Инициализируем значения по умолчанию, если они не указаны
      item.quantity = item.quantity || 0;
      item.price = item.price || 0;
      item.discount = item.discount || 0;

      // Рассчитываем сумму
      if (item.quantity && item.price) {
         item.sum = item.quantity * item.price;
      }

      // Рассчитываем сумму со скидкой
      if (item.sum && item.discount) {
         item.sum_with_discount = item.sum * (1 - item.discount / 100); // В коде используем JavaScript-синтаксис
      }
       else {
         item.sum_with_discount = item.sum;
      }

      this.items.push(item);
      this.calculateTotal();
      return item;
   },

   // Метод для удаления товара
   removeItem(index) {
      if (!this.items || index >= this.items.length) return false;

      this.items.splice(index, 1);
      this.calculateTotal();
      return true;
   },

   // Метод для обновления товара
   updateItem(index, updatedItem) {
      if (!this.items || index >= this.items.length) return false;

      // Обновляем поля товара
      const item = this.items[index];
      Object.assign(item, updatedItem);

      // Пересчитываем суммы
      if (item.quantity && item.price) {
         item.sum = item.quantity * item.price;
      }

      if (item.sum && item.discount) {
         item.sum_with_discount = item.sum * (1 - item.discount / 100);
      }
       else {
         item.sum_with_discount = item.sum;
      }

      this.calculateTotal();
      return true;
   },

   // Метод для изменения ставки НДС
   setVatRate(rate) {
      if (rate < 0) return false;

      this.vat_rate = rate;
      this.calculateTotal();
      return true;
   },

   // Метод для получения итоговой информации о накладной
   getSummary() {
      return {
         number: this.number,
         date: this.formattedDate,
         supplier: this.supplier,
         recipient: this.recipient,
         itemsCount: this.items?.length || 0,
         totalSum: this.total_sum,
         totalDiscount: this.total_discount,
         totalWithDiscount: this.total_with_discount,
         vatRate: this.vat_rate,
         vatSum: this.vat_sum,
         totalWithVat: this.total_with_vat,
         status: this.status
      };
   }
}