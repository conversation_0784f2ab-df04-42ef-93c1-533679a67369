export default {
    allowSave: true
}
VIEW({ imports: '@tools/jupyter, @tools/jupyter-tree, @lib/item-node', extends: 'oda-jupyter',
    template: /*html*/ `
        <oda-jupyter-tree slot="right-panel" ::focused-row="focusedCell" label="Contents"></oda-jupyter-tree>`
    ,
    $public: {
        get url() {
            return this.contextItem?.url;
        }
    },
    get readOnly(){
        return this.contextItem?.readOnly;
    },
    async save() {
        if (!this.isChanged) return;
        return this.contextItem.save(this.notebook.data);
    },
    $listeners: {
        changed(e) {
            this.contextItem.isChanged = true;
        }
    },
    async loadFile(path){
        let file  = await this.contextItem.getFile(path);
        if (!file) throw new Error('file not found!')
        return file.load()
    },
    saveFile(fileBody, path = '', inherit = true, mime){
        return this.contextItem.saveFile(fileBody, path, inherit, mime);
    },
    createElement(...args){
        return ODA.createElement(...args);
    }
})
