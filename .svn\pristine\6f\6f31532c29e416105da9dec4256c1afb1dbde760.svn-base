import {webgpu} from './web-gpu.js';
export class tensor/* extends Array*/{
    #data = null;
    #grad = null;
    dType = torus.DEFAULT_TYPE;
    #src = undefined;
    #type = undefined;
    #shape_multipliers = undefined;
    isParam = false;
    backs = [];
    step = 0;
    id = genId();
    constructor(data, $ = {dType: torus.DEFAULT_TYPE}) {
        $ = torus.$($)
        if(data === undefined)
            this.#data = new ($.dType)(0);
        else if (data?.$ === this.constructor.name){
            this.dType = globalThis[data.dType];
            this['#shape'] = data.shape;
            this.isParam = data.isParam;
            this['#label'] = data.label;
            this.isSerializable = data.isSerializable;
            data = data.data.split(' ');
            this.#data = new this.dType(data);
 
        }
        else{

            if (Array.isArray(data)){
                let shape = [data.length];
                let next;
                while ((next = data[0]) && Array.isArray(next)){
                    shape.push(next.length);
                    data = data.flat();
                }
                if (next instanceof tensor){
                    $.dType = next.dType
                    shape.push(...next.shape);
                    let size = next.size;
                    next = new $.dType(shape.mul());
                    data = data.reduce((r, v, i)=>{
                        r.set(v.data, i * size);
                        return r
                    }, next);
                }
                else {
                    if(!(data instanceof $.dType))
                        data = new $.dType(data);
                }
                this['#shape'] = shape;
            }
            else{
                if (data?.length === 1)
                    this['#shape'] = [1]
                else if (data?.length)
                    this['#shape'] = [data?.length]
                else if (!data?.buffer){
                    // this['#shape'] = [1];
                    data = new this.dType([data])
                }

            }
            this.#data = data;
        }
        this.dType = this.data.constructor
    }
    _resize_data(data, ...shape){
        // while (shape.some(i=>Array.isArray(i)))
        //     shape = shape.flat();
        shape = torus.flat(shape);
        const size = shape.mul();
        if (size !== data.length)
            throw new Error(`_shape from (${this.shape}) to (${shape}) not allow.`);
        this.#data = data;
        this['#shape'] = shape
        return this;
    }

    getPath(level = 0){
        let tab = '|'.repeat(level) + '|- '
        let path = [tab + this.label];
        let src = this.#src?.map(v=>v.getPath(level+1));
        if(src){
            path.push(...src.flat());
        }
        return path;
    }
    get path(){
        return this.getPath().join('\n');
    }
    get shape_multipliers(){
        return this.#shape_multipliers ??= this.shape.map((_,i)=> this.shape.slice(i+1).mul());
    }
    unsqueeze(dim){
        let shape = [...this.shape];
        shape.splice(dim, 0, 1);
        return torus.from(this.data)._shape(shape);
    }
    get freezed(){
        return this._freezed ??= torus.from(this.data)._shape(this)._label((this.label || '') + ' freezed');
    }
    toJSON(){
        const result =  {
            $: this.constructor.name,
            label: this.label,
            shape: this.shape,
            isSerializable: this.isSerializable,
            isParam: this.isParam,
            dType: this.dType.name,
        }
        if (this.isDestroyed)
            result.data = 'DESTROYED';
        // else if (torus.QUANTIZATION){
        //     let q = torus.QUANTIZATION;
        //
        //     blob = new Blob([a], { type: 'application/octet-stream' });
        //
        //
        //     result.data = this.data
        //     Array.from(this.data || []).map(val=>{
        //         let add = val<0?2:1
        //         let s = val.toString();
        //         let idx = s.indexOf('.');
        //         if (idx === -1){
        //             return s;
        //         }
        //         return s.substring(0, idx + q + add)
        //     }).join(' ');
        //
        //     // result.data = Array.from(this.data || []).map(val=>{
        //     //     let add = val<0?2:1
        //     //     let s = val.toString();
        //     //     let idx = s.indexOf('.');
        //     //     if (idx === -1){
        //     //         return s;
        //     //     }
        //     //     return s.substring(0, idx + q + add)
        //     // }).join(' ');
        // }
        else {
            result.data = this.data.join(' ');
            // let blob = new Blob([this.data], {type: 'application/octet-stream'});
            // blob.text().then(res=>{
            //     result.data = res;
            // })

        }
        return result;
    }
    _label(label){
        this['#label'] = label;
        return this;
    }
    _src(...tensors){
        tensors = torus.flat(tensors);
        this.#src = tensors;//.filter(t=>t.allowGrad);
        return this;
    }
    get src(){
        return this.#src;
    }
    _dType(dType){
        if (this.dType !== dType){
            this.dType = dType;
            const data = new dType(this.data.length);
            let i = this.size;
            while(i--)
                data[i] = this.data[i];
            this.#data.buffer.transfer(0);
            this.#data = data;
        }
        return this;
    }
    _data(data){
        this.#data = data;
        return this;
    }
    _param(){
        this.isParam = true;
        return this;
    }
    reshape(...shape){
        return this._shape(shape);
        // return this._shape(...shape);
    }
    resize(...shape){
        return this._shape(shape);
        // return this._shape(...shape);
    }
    _shape(...shape_or_tensor) {   // shape or tensor
        const shape = this.check_shape(shape_or_tensor);
        this.#shape_multipliers = undefined;
        this['#shape'] = shape;
        return this;
    }

    //inplace functions
    mul_(factor){
        this.#data = this.data.map(d=>d * factor);
        return this;
    }
    div_(factor){
        this.#data = this.data.map(d=>d / factor);
        return this;
    }
    plus_(factor){
        this.#data = this.data.map(d=>d + factor);
        return this;
    }
    minus_(factor){
        this.#data = this.data.map(d=>d - factor);
        return this;
    }
    get OUTS(){
        if (this.allowGrad)
            this.__outs__ ??= Object.create(null);
        return this.__outs__;
    }
    set OUTS(n){
        if (!this.__outs__)
            this.__outs__ = n;
    }
    get allowGrad(){
        return (this.isParam || !!this.src?.some(i=>i.allowGrad));
    }
    get data(){
        return this.#data;
    }
    set data(n){
        this['#size'] = undefined;
        this.#grad = undefined;
        if (n.length !== this.size)
            throw new Error(`set data(n): dimension out of range (expected ${this.#data.length}, but got ${n.length})`);
        this.#data = n
        this.dType = this.#data.constructor;
    }

    fill(value = 0){
        if(this.gpuDataBuffer){
            if(value === 0){
                torus.WebGPU.clearBuffer(this.gpuDataBuffer);
            }
            else{
                let ws = Math.min(256, this.size);
                let cb = new CodeBuilder(
                    `@group(0) @binding(0) var<storage, read_write> t${this.id}: array<${this.gpuType}>;`,
                    `@compute @workgroup_size(${ws})`,
                    `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
                    `    if(id.x >= ${this.size}) {return;}`,
                    `    t${this.id}[id.x] = ${value}; // заполняем буфер значением`,
                    `}`
                )
                torus.WebGPU.run(cb.code, [this], [Math.ceil(this.size / ws)]);
            }
        }
        else{
            this.data.fill(value);
        }
    }
    get grad(){
        if (!this.#grad){
            let data = new Float32Array(this.size);
            this.#grad = torus.from(data)._shape(this.shape)._label('GRAD for: '+this.label);
            this.#grad.OUTS = Object.create(null);
        }
        return this.#grad;
    }
    set grad(n){
        this.#grad = n;
    }
    get BiTES_PER_ELEMENT(){
        return this.dType.BYTES_PER_ELEMENT * 8;
    }
    get T(){
        return this.transpose();
    }
    get shape(){
        return this['#shape'] || [];
    }
    get size(){
        return this['#size'] ??= (this.shape.mul() || this.data.length); //У скаляров размерность 0, а количество элементов 1
    }
    get dim(){
        return this.shape.length;
    }
    get label(){
        return this['#label'];
    }
    get type(){
        return this.#type ?? (()=>{
            switch (this.dim){
                case 0:{
                    if (!this.size)
                        return 'empty';
                    return `scalar`;
                }

                case 1:{
                    if(this.shape.mul()<2)
                        return `scalar`;
                    return `vector`;
                }
                case 2:
                    return `matrix`;
                default:
                    return `tensor`;
            }
        })();
    }

    get paramCount(){
        if (this.isParam)
            return this.size;
        return 0;
    }
    destroy(recurce = true){
        if(this.isParam) return;
        if (!this.data.length) return;
        this.data.buffer.transfer(0);
        this.isDestroyed = true;
        if (!recurce) return;
        if (!this.src?.length) return
        this.src.forEach(s=>s.destroy(recurce))
    }
    update_params(){
        if (!this.isParam)
            return;
        if(this.gpuDataBuffer){
            let ws = Math.min(this.size,  256);
            if(!this.gpuUpdateParamsCode) {
                let cb = new CodeBuilder(
                    `const LEARNING_RATE: f32 = ${torus.LEARNING_RATE};`,
                    `@group(0) @binding(0) var<storage, read> t${this.grad.id}: array<${this.grad.gpuType}>;`,
                    `@group(0) @binding(1) var<storage, read_write> t${this.id}: array<${this.gpuType}>;`,
                    `@compute @workgroup_size(${ws})`,
                    `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
                    `    let idx = id.x;`,
                    `    if(idx>=${this.size}) {return;} `,
                    `    t${this.id}[idx] = t${this.id}[idx] - t${this.grad.id}[idx] * LEARNING_RATE;`,
                    `}`
                )
                this.gpuUpdateParamsCode = cb.code;
            }
            this.gpuUpdateParamsCommandEncoder = torus.WebGPU.run(this.gpuUpdateParamsCode, [this.grad, this], [Math.ceil(this.size / ws)]);
        }
        else{
            let data = this.data;
            let grad = this.grad.data;
            for (let i = 0; i<this.size; i++){
                let value = data[i]  - grad[i] * torus.LEARNING_RATE;
                if (!Number.isFinite(value))
                    throw new Error(`Error update_param value ${value} on tensor: ` + this.label)
                data[i] = value;
            }
        }
    }
    back(grad){
        let topo = [];
        let visited = new Set();
        let build_topo = (t) => {
            if (!visited.has(t)) {
                visited.add(t);
                t.src?.filter(t=>t.allowGrad).forEach(ch => build_topo(ch));
                topo.push(t);
            }
        }
        build_topo(this);
        topo.reverse();

        for(let node of topo){
            if (node.allowGrad){
                if(torus.USE_GPU)
                    node.grad.writeToGPU();
                node.grad.fill(0);
            }

        }

        if(grad){
            if (grad.constructor === Number)
                topo[0].grad.fill(grad);
            else
                throw Error(`Unknown value ${grad} for gradients`);
        }

        for(let node of topo){
            if (!node.src?.length || !node.allowGrad)
                continue;
            node._back?.();
        }

        topo = topo.filter(t=>t.isParam);
        for(let t of topo)
            t.update_params();
    }
    backward(grad){
        return this.back(grad);
    }

    static get DEFAULT_TYPE(){
        return globalThis.DEFAULT_TYPE || Float32Array;
    }
    static set DEFAULT_TYPE(n){
        globalThis.DEFAULT_TYPE  = n;
    }

    static get LEARNING_RATE(){
        return globalThis.LEARNING_RATE || .1;
    }
    static set LEARNING_RATE(n){
        globalThis.LEARNING_RATE  = n;
    }

    static get USE_GET_OUT(){
        return globalThis.USE_GET_OUT || false;
    }
    static set USE_GET_OUT(n){
        globalThis.USE_GET_OUT  = n;
    }

    static get USE_GPU(){
        return globalThis.USE_GPU || false;
    }
    static set USE_GPU(n){
        globalThis.USE_GPU  = n;
    }

    static get USE_SCALAR_CACHE(){
        return globalThis.USE_SCALAR_CACHE || false;
    }
    static set USE_SCALAR_CACHE(n){
        globalThis.USE_SCALAR_CACHE  = n;
    }

    static get QUANTIZATION(){
        return globalThis.QUANTIZATION || 0;
    }
    static set QUANTIZATION(n){
        globalThis.QUANTIZATION  = n;
    }
    
    static get TURBO(){
        return globalThis.TURBO || false;
    }
    static set TURBO(n){
        globalThis.TURBO  = n;
    }


    static get generator(){
        return torus.__random_generator__;
    }
    static manual_seed(seed){
        if (seed) {
            seed %= 2147483647; //Защита от перехода в бесконечность
            if (seed<0)
                seed += 2147483647;
                //throw new Error(`'seed' must be positive number`);
            const gen = pseudoRandom(seed);
            torus.__random_generator__ = ()=>{
                return (gen.next().value / 2147483647);
            };
        }
        else
            torus.__random_generator__ = Math.random;
        return torus.__random_generator__;
    }

    static _scalars_tensors = {};
    static from(data_or_tensor, $){
        if (!Object.equal(data_or_tensor?.constructor, tensor)){
            if (torus.USE_SCALAR_CACHE && Number.isFinite(data_or_tensor)){
                data_or_tensor = torus._scalars_tensors[data_or_tensor] ??= (new tensor(data_or_tensor, $))._label('SCALAR')
            }
            else
                data_or_tensor = new tensor(data_or_tensor, $);
        }

        if (this !== torus && this?.OUTS)
            data_or_tensor.OUTS ??= this.OUTS;
        return data_or_tensor;
    }
    static param(src){
        src = tensor.from(src);
        src.isParam = true;
        src.isSerializable = true;
        if(torus.USE_GPU && webgpu.device)
            src.writeToGPU();
        return src;
    }
    static get WebGPU(){
        return webgpu;
    }
    get gpuType(){
        switch (this.dType){
            case Float32Array:
                return 'f32';
            case Float64Array:
                return 'f62';
            case Int8Array:
                return 'i8';
            case Int16Array:
                return 'i16';
            case Int32Array:
                return 'i32';
            case Uint8Array:
                return 'u8'
            case Uint16Array:
                return 'u16';
            case Uint32Array:
                return 'u32';
        }
    }
    gpuDestroy(){
        this.gpuDataBuffer?.destroy();
        this.gpuDataBuffer = null;
        webgpu.destroy(this.data);
    }
    get gpuBuffer(){
        return this.gpuDataBuffer;
    }
    writeToGPU(copy = false){
        if(!torus.USE_GPU || !webgpu.device)
            throw new Error('GPU not supported');
        this.gpuDataBuffer = webgpu.writeData(this.data, copy, 't' + this.id);
        return this.gpuDataBuffer;
    }
    async readFromGPU(){
        try{
            if(!webgpu.device)
                throw new Error('GPU not supported');
            let data = await webgpu.readData(this.#data);
            this.#data.set(data);
            return this.#data;
        }
        catch (e){
            console.warn(e.message)
        }
    }
    static flat(...shape){
        return shape.flat(Infinity);
    }
    reverse(dim = 0){
        if (-this.dim > dim || this.dim - 1 < dim)
            throw new Error(`tensor.reverse(${dim}): dimension out of range (expected to be in range of [-${this.dim}, ${this.dim - 1}], but got ${dim})`)
        if (dim < 0)
            dim += this.dim;


        this.data.reverse();

        return this;
    }
    repeat(...repeat_shape){
        if (repeat_shape.length === 1){
            if (Number.isInteger(repeat_shape[0])){
                repeat_shape = [repeat_shape[0]];
            }
            else if (Array.isArray(repeat_shape[0])){
                repeat_shape = repeat_shape[0];
            }
        }
        const multiply = repeat_shape.mul();
        const new_size = this.size * multiply;
        let data = new this.dType(new_size);
        const old_size = this.data.length;
        for (let i = 0; i < old_size; i++){
            let d = this.data[i]
            for (let m = i; m < new_size; m += old_size){
                data[m] = d;
            }
        }
        this.#data = data;
        this._shape([...repeat_shape, ...this.shape]);
        return this;
    }
    toString(step = 0, max = 8){
        let data = this.array.toTensorString(step, max, this.shape, this.dType).split('\n');
        data = data.join('\n');
        let tab = ('  ').repeat(step)
        let result  = tab + this.type + ` ${this.label || ''}: `;
        if (this.dim > 1 || this.shape.last>1)
            result += `shape(${this.shape}), size(${this.size.toLocaleString()}), ${this.dType.name}, ${this.backs.join(',')}\n${tab}[${data}]`;
        else
            result += `${this.dType.name}, ${this.backs.join(',')}\n${tab}(${data.replaceAll('[', '').replaceAll(']', '').trim()})`;
        result = ' (id#'+this.id + '): ' + result
        if(this.gpuDataBuffer)
            result = ' GPU ' + result
        if (this.isParam)
            result = tab + 'PARAM' + result;
        return result + '\n';
    }
    get array() {
        if(this.shape.length<2)
            return [this.data];
        let data = Array.from(this.data);
        let res = [];
        const shape = Array.from(this.shape);
        let s
        while (s = shape.pop()){
            const size = data.length;
            for (let i = 0; i < size; i += s){
                res.push(Array.from(data.slice(i, i + s)))
            }
            data = res;
            res = [];
        }
        return data.flat();

    }
}

export class CodeBuilder{
    constructor(...code_parts){
        this.parts = code_parts;
    }
    get code(){
        return this.parts.join('\n')
    }
}
export const tt = tensor;
export const torus = tensor;
torus.__random_generator__ = Math.random;
function* pseudoRandom(seed) {
    let value = seed * 16807 % 2147483647

    while(true) {
        value = value * 16807 % 2147483647

        yield value;
    }

}
// let generator = pseudoRandom(1);

torus.prototype.item = function (...shape){
    shape = torus.flat(shape);
    //todo
}

torus.genVarsArray = function(size, upper = false){
    let char_code = upper?65:97;
    return Array(size).fill().map((_,i)=>String.fromCharCode(i + char_code))
}

torus.prototype.dot = function (other){
    if(this.shape.last !== other.shape.last)
        throw new Error(`dot: last dimentions of both tensors must be equal`);
    let x = this.shape_info.map(ax=>ax.char).join('');
    let y = other.shape_info.map(ax=>ax.char).join('');
    let exit = (x.length<y.length?y:x).slice(0, -1)
    let expr = `${x}, ${y} -> ${exit}`;
    return torus.einsum(expr, [this, other])._label('dot product ('+expr+')');
}

torus.prototype.findIndex = function(...indices){
    indices = torus.flat(indices);
    return indices.reduce((r, v, i)=> (r + v * this.shape_multipliers[i]), 0)
}
torus.prototype.item = function(...indices) {
    return this.get(...indices)
}
torus.prototype.get = function(...indices){
    indices = torus.flat(indices);
    const idx = indices.reduce((r, v, i)=> (r + v * this.shape_multipliers[i]), 0)
    if(!indices.length  || indices.length === this.shape_multipliers.length)
        return this.data[idx];
    return this.data.slice(idx, idx + this.shape_multipliers.slice(indices.length-1).mul())
}
torus.prototype.set = function(value, ...indices){
    indices = torus.flat(indices);
    const idx = indices.reduce((r, v, i)=>(r + v * this.shape_multipliers[i]), 0)
    this.data.set(value.data || torus.flat(value), idx);
}


torus.prototype.log_ = function (){
    let i = this.data.length
    while(i--){
        this.data[i] = Math.log(this.data[i]);
    }
    return this;
}

torus.prototype.allclose = function(other, rtol = 1e-05, atol = 1e-08, equal_nan = false ){
    const fn = equal_nan?(r, y, i)=>(r && (this.data[i] || 0) - (y || 0) <= atol + rtol * (y || 0)):((r, y, i)=>r && this.data[i] - y <= atol + rtol * y)
    return other.data.reduce(fn, true);
}

torus.prototype.masked_fill = function(other, mask = 0, value = 0){
    const forward = `(x, y) => (y === ${mask}?${value}:x)`;
    return torus._element_wise.call(this, {forward, backward:'()=>1'}, this, other)._label(`masked_fill(mask=${mask}, value=${value})`);
}
torus.prototype.add = torus.prototype.plus = function (other){
    return torus._element_wise.call(this, {forward: '(x, y) => x + y', backward: '() => 1'}, this, other)._label('plus: '+ this.shape + ' + ' + (other?.shape || other));
}
torus.prototype.minus = torus.prototype.sub = torus.prototype.substract = function (other){
    return torus._element_wise.call(this, {forward: '(x, y) => x - y', backward_0: '() => 1', backward_1: '() => -1'}, this, other)._label('sub');
}
torus.prototype.mul = torus.prototype.multiply = function (...other){
    if (other.length>1){
        let p = {forward: '(...val) => val.mul()'}
        for (let i = 0; i<other.length + 1; i++){
            p['backward_'+i] = `(...val) => val.filter((_, i)=>i != ${i}).mul()`
        }
        return torus._element_wise.call(this, p, this, ...other)._label('mul');
    }
    return torus._element_wise.call(this, {forward: '(x,y) => x*y', backward_0: '(...val) => val[1]', backward_1: '(...val) => val[0]'}, this, ...other)._label('mul');
}
torus.prototype.div = torus.prototype.divide = function (other){
    // rounding_mode: 'ceil', 'round', 'floor'
    return torus._element_wise.call(this, {forward:  '(x, y) => x / y', backward_0: '(x, y) => (1/y)', backward_1: '(x,y)=>(-x / (y ** 2))'}, this, other)._label('div');
}
torus.prototype.pow = function (other){
    return torus._element_wise.call(this, {forward:  '(x, y) => x ** y', backward_0: '(x, y) => (y * (x ** (y - 1)))', backward_1: '(x, y) => ((x ** y) * Math.log(x))'}, this, other)._label('pow');
}
torus.$ = function (...$){
    return Object.assign({keepdim: false, dType: torus.DEFAULT_TYPE}, ...$)
}

torus.prototype.softmax = function (dim = -1){ //todo out._back
    let key  = 'softmax: '+ dim;
    let out = torus.get_out(this, key);
    if (!out){
        let size = this.size;
        out =  torus.from(new Float32Array(size))._src(this)._label('softmax')._shape(this);
        torus.set_out(this, out, key);
        dim = this.dim_info(dim)[0];
        let groups = size / dim.size;
        let step = dim.stride * dim.size;
        if(this.gpuDataBuffer){
            out.writeToGPU();
            let wgs = [Math.min(groups, 256)];
            let cb = new CodeBuilder(
                `@group(0) @binding(0) var<storage, read> input: array<f32>;`,
                `@group(0) @binding(1) var<storage, read_write> output: array<f32>;`,
                `@compute @workgroup_size(${wgs})`,
                `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
                `   if(id.x >= ${groups}) {return;}`,
                `   let start = id.x % ${dim.stride}u + (id.x / ${dim.stride}u) * ${step}u;`,
                `   var max_val = input[start];`,
                `   for (var i: u32 = 1; i < ${dim.size}u; i++) {`,
                `       var v = input[start + (i * ${dim.stride}u)];`,
                `       max_val = max(max_val, v);`,
                `   }`,
                `   var sum = 0.0;`,
                `   for(var i: u32 = 0; i<${dim.size}u; i++){`,
                `       let idx = start + (i * ${dim.stride}u);`,
                `       var v = input[idx] - max_val;`,
                `       v = exp(v);`,
                `       output[idx] = v;`,
                `       sum += v;`,
                `   }`,
                `   for(var i: u32 = 0; i<${dim.size}u; i++){`,
                `       let idx = start + (i * ${dim.stride}u);`,
                `       output[idx] = output[idx] / sum;`,
                `   }`,
                `}`
            )
            let code = cb.code;
            wgs[0] = Math.ceil(groups / wgs[0]);
            out._fwd = ()=> {
                torus.WebGPU.compute(code, [this, out], wgs);
                return out;
            }
        }
        else{

            out._fwd = ()=> {
                for(let g = 0; g<groups; g++){
                    let start = g % dim.stride + Math.trunc(g / dim.stride) * step;
                    let max = this.data[start];
                    for(let i = 1; i<dim.size; i++){
                        let v = this.data[start + i * dim.stride];
                        max = Math.max(v, max);
                    }
                    let sum = 0;
                    for(let i = 0; i<dim.size; i++){
                        let idx = start + i * dim.stride;
                        let v = this.data[idx] - max;
                        out.data[idx] = v = Math.exp(v);
                        sum += v;
                    }
                    for(let i = 0; i<dim.size; i ++){
                        let idx = start + i * dim.stride;
                        out.data[idx] = out.data[idx] / sum;
                    }
                }
                return out;
            }
        }
    }
    return out._fwd();
}


torus.prototype.maxIndex = function () {
    let step = this.shape.last;
    let data = new Uint8Array(this.size / step);
    let idx = -1;
    for (let i = 0; i<this.size; i+=step){
        const slice = this.data.slice(i, i + step)
        const max = Math.max(...slice);
        data[++idx] = slice.indexOf(max);
    }
    const out = tensor.from(data)._label('maxIndex')._shape(this.shape.slice(0, -1));
    return out;
}

torus.prototype.hardmax = function (){
    const step = this.shape[this.shape.length-1];
    const size = this.size/step;
    const data = new torus.DEFAULT_TYPE(this.size);
    for (let x = 0; x<size; x++){
        let max = undefined;
        let idx;
        for (let y = 0; y<step; y++){
            let v = this.data[y + step * x];
            if (max === undefined || max < v){
                idx = y
                max = v;
            }
        }
        for (let y = 0; y<step; y++){
            data[y + step * x] = (idx === y)?1:0;
        }
    }
    const out =  tensor.from(data)._src(this)._label('hardmax')._shape(this);
    return out;
}

torus.prototype.MSE = torus.prototype.mse_loss = function (target) {
    const this_shape = this.shape.toReversed();
    const target_shape = target.shape.toReversed();
    if ( !target_shape.every((v, i) => v === this_shape[i] || i >= this_shape.length) )
        throw new Error(`predicted_tensor.MSE(target): ` +
            `The shape (${this.shape}) of predicted tensor mismatches the shape (${target.shape}) of target tensor`);
    let out = torus.get_out(this, 'MSE');
    if (!out) {
        out = torus.from(new torus.DEFAULT_TYPE(1))._src(this)._label('mse_loss');
        torus.set_out(this, out, 'MSE');
        let error = torus.from(new Float32Array(this.size));
        if(this.gpuDataBuffer){
            out.writeToGPU();
            error.writeToGPU();
            let workGroups =   Math.ceil(error.size / 256);

            let cb = new CodeBuilder(
                `@group(0) @binding(0) var<storage, read> t${this.id}: array<${this.gpuType}>;`,
                `@group(0) @binding(1) var<storage, read> target_buf: array<${target.gpuType}>;`,
                `@group(0) @binding(2) var<storage, read_write> t${error.id}: array<${error.gpuType}>;`,
                `@compute @workgroup_size(256)`,
                `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
                `    let globalIdx = id.x;`,
                `    if(globalIdx >= ${error.size}) {return;}`,
                `    let x = t${this.id}[globalIdx];`,
                `    t${error.id}[globalIdx] = x - target_buf[globalIdx % ${target.size}];`,
                `}`)
            let fwd_code = cb.code;
            cb = new CodeBuilder(
                `@group(0) @binding(0) var<storage, read> t${error.id}: array<${error.gpuType}>;`,
                `@group(0) @binding(1) var<storage, read_write> t${out.id}: array<${out.gpuType}>;`,
                `@compute @workgroup_size(1)`,
                `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
                `    let globalIdx = id.x;`,
                `    if(globalIdx >= 1) {return;}`,
                `    var sum = 0.0;`,
                `    for(var i = 0u; i<${error.size}u; i += 1 ){`,
                `      let e = t${error.id}[i];`,
                `       sum += e * e;`,
                `    }`,
                `    sum = sum / ${error.size};`,
                `    t${out.id}[globalIdx] = sum;`,
                `}`)
            let fwd_code1 = cb.code;
            out._fwd = (target) => {
                let tar = target.writeToGPU();
                tar.label = 'target_buf';
                torus.WebGPU.run(fwd_code, [this, tar, error], [workGroups]);
                target.gpuDestroy();
                torus.WebGPU.run(fwd_code1, [error, out], [1]);
                return out;
            }
            if(this.allowGrad){
                cb = new CodeBuilder(
                    `@group(0) @binding(0) var<storage, read> t${error.id}: array<${error.gpuType}>;`,
                    `@group(0) @binding(1) var<storage, read_write> t${this.grad.id}: array<${this.grad.gpuType}>;`,
                    `@compute @workgroup_size(255)`,
                    `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
                    `    let globalIdx = id.x;`,
                    `    if(globalIdx >= ${this.size}) {return;}`,
                    `    t${this.grad.id}[globalIdx] += t${error.id}[globalIdx] * 2;`,
                    `}`)
                let code = cb.code;
                out._back = ()=>{
                    torus.WebGPU.run(code, [error, this.grad], [workGroups]);
                }
            }
        }
        else{
            out._fwd = (target) => {
                let loss = 0;
                for (let i = 0; i<this.size; i++){
                    loss += (error[i] =  this.data[i] - target.data[i % target.size]) ** 2;
                }
                loss /= this.size;
                out.data.set([loss]);
                return out;
            }
            out._back = ()=>{
                for (let i = 0; i<this.size; i++)
                    this.grad.data[i] += error[i] * 2;
            }
        }

    }
    return out._fwd(target);
}

torus.prototype.repeat = function (count = 1) {
    return tensor.from(Array(count).fill().map(i=>this));
}

torus.prototype.cross_entropy = torus.prototype.crossEntropy = function (target) {
    if (this.label !== 'softmax'){
        return this.softmax().cross_entropy(target);
    }
    target = torus.from(target);
    let out = torus.get_out(this, 'cross_entropy');
    if (!out){
        if (!Object.equal(this.shape, target.shape, true)){
            if (!Object.equal(this.shape.slice(0, -1), target.shape, true))
                throw new Error(`cross_entropy: Expected input batch_size (${this.shape.slice(0, -2).mul()}) to match target batch_size (${this.shape.mul()})`);
        }
        out = torus.from(new Float32Array(this.size / this.shape.last))._src(this)._label('crossEntropy');
        torus.set_out(this, out, 'cross_entropy');
        let size = this.size;
        let stride = this.size / target.size;
        if(torus.USE_GPU && this.gpuDataBuffer){
            out.writeToGPU();
            let cb;
            let wgs = [Math.min(out.size, 256)];
            if (this.size === target.size){
                cb = new CodeBuilder(
                    `@group(0) @binding(0) var<storage, read_write> predictions: array<f32>;`,
                    `@group(0) @binding(1) var<storage, read> targets: array<${target.gpuType}>;`,
                    `@group(0) @binding(2) var<storage, read_write> out: array<f32>;`,
                    `@compute @workgroup_size(${wgs})`,
                    `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
                    `   let idx = id.x;`,
                    `   if(idx >= ${out.size}) {return;}`,
                    `   let start = id.x * ${this.shape.last};`,
                    `   let end = start + {this.shape.last};`,
                    `   for (var i: u32 = start; i < end; i++){`,
                    `       let y = targets[i];`,
                    `       if(y) {`,
                    `           let x = clamp(predictions[i], 1e-7, 1.0 - 1e-7);`,
                    `           predictions[i] = x - y;`,
                    `           out[idx] = -log(x);`,
                    `           break;`,
                    `       }`,
                    `   }`,
                    `}`
                )
            }
            else{
                cb = new CodeBuilder(
                    `@group(0) @binding(0) var<storage, read_write> predictions: array<f32>;`,
                    `@group(0) @binding(1) var<storage, read> targets: array<${target.gpuType}>;`,
                    `@group(0) @binding(2) var<storage, read_write> out: array<f32>;`,
                    `@compute @workgroup_size(${wgs})`,
                    `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
                    `   let idx = id.x;`,
                    `   if(idx >= ${out.size}) {return;}`,
                    `   var i = idx * ${stride} + u32(targets[idx]);`,
                    `   let x = clamp(predictions[i], 1e-7, 1.0 - 1e-7);`,
                    `   predictions[i] = x - 1;`,
                    `   out[idx] = -log(x);`,
                    `}`
                )
            }
            let code = cb.code;
            wgs[0] = Math.ceil(out.size / wgs[0]);
            out._fwd = (target)=>{
                target.writeToGPU();
                torus.WebGPU.run(code, [this, target, out], wgs);
                target.gpuDestroy();
                return out;
            }
            if(this.allowGrad){
                this._back = ()=>{
                    this.copyGpuBufferTo(this.src[0].grad);
                }
            }
        }
        else{
            out._fwd = (target)=>{
                if (this.size === target.size){
                    let oidx = 0;
                    for (let i = 0; i < size; i++){
                        let y = target.data[i];
                        if(!y) continue;
                        let x = Math.min(Math.max(this.data[i], 1e-7), 1.0 - 1e-7);
                        this.data[i] = x - y;
                        out.data[oidx++] = -Math.log(x);
                    }
                }
                else {

                    let t_size = target.size;
                    for(let i = 0; i < t_size; i++){
                        let j = target.data[i];
                        let idx = i * stride + j;
                        let x = Math.min(Math.max(this.data[idx], 1e-7), 1.0 - 1e-7);
                        this.data[idx] = x - 1;
                        out.data[i] = -Math.log(x);
                    }
                }
                return out;
            }
            if(this.allowGrad){
                this._back = ()=>{ // this - ПРАВИЛЬНО, т.к. _back подменяется у SOFTMAX;
                    this.src[0].grad.data.set(this.data);
                }
            }
        }

    }
    return out._fwd(target);
}

if (!Array.prototype.toTensorString) {
    Object.defineProperty(Array.prototype, 'toTensorString', {
        configurable:true,
        enumerable:false,
        value (step = 0, max = 4, shape = [], dType = torus.DEFAULT_TYPE) {
            let float_type = dType.name[0] === 'F'
            function recurse(d, idx = 0, l = 0){
                let result = (idx?`\n${('  ').repeat(step)+(' ').repeat(l)}[`:'[');
                if (d[0]?.map){
                    let list = Array.from(d).map((v, i)=>{
                        return recurse(v, i, l + 1)
                    })
                    result += list.join(',');
                }
                else{
                    if (d.length > max){
                        const showing = Math.floor(max/2);
                        result += Array.from(d.slice(0, showing)).map(x=>{
                            return  num2text(x, float_type);
                        }).join(',') ;
                        result +=  `,  ...,`;
                        result +=  Array.from(d.slice(-showing)).map(x=>{
                            return num2text(x, float_type);
                        }).join(',');
                    }
                    else{
                        result += Array.from(d).map(x=>{
                            return num2text(x, float_type);
                        }).join(',') || num2text(d, float_type);
                    }
                }

                result = result + ']';
                return result;
            }
            let res = recurse(this);
            res = res.slice(1, -1);
            res = res.replaceAll(']],', ']],\n')
            return res;
        }
    } )
}
// let max = 8;


function num2text(x, float_type = false, text_max = 8) {
    let num = ' '.repeat(Math.sign(x)===-1 ? 1: 2);
    if (!Number.isFinite(x)) {
        num += x.toString();
        num = num.substring(0, 5);
    }
    else {
        const x_abs = Math.abs(x);
        const showAsExponential = x_abs >= 10**(text_max-3 + (Number.isInteger(x) && !float_type)) || (x_abs < 0.01 && x!==0);
        if (showAsExponential) {
            let mantissa;
            if (x_abs >= 1e+100 || x_abs < 1e-99)
                mantissa = text_max-9<1 ? 1: text_max-9;
            else if (x_abs >= 1e+10 || x_abs < 1e-9)
                mantissa = text_max-8<1 ? 1: text_max-8;
            else
                mantissa = text_max-7<1 ? 1: text_max-7;
            num += x.toExponential(mantissa);
            //Рассчитанная выше длина мантиссы считалась для исходного числа.
            //При этом метод toExponential перед формированием строки округляет число, что может изменить порядок числа.
            //Например, для числа 9.999999e+9 была рассчитана длина мантиссы 2, и, следовательно, ожидалась строка «9.99e+9» из семи символов.
            //Однако метод toExponential округляет число и выдаёт строку «1.00e+10» длинной восемь символов,
            //т.к. в порядке стало две цифры, а длина мантиссы рассчитывалась для одной цифры.
            //Ещё пример, для числа 9.999999e-10 была рассчитана длина мантиссы 1, и, следовательно, ожидалась строка «9.9e-10» из семи символов.
            //Однако метод toExponential округляет число и выдаёт строку «1.0e-9» длинной шесть символов,
            //т.к. в порядке оказалась одна цифра, а длина мантиссы рассчитывалась для двух цифр.
            //При выводе многомерных тензоров разная длина чисел будет ломать стройный вид столбцов.
            //Следующие пять строчек кода устраняют этот недостаток.
            if (num.length < text_max )   //Если длина строки меньше заданной увеличиваем количество цифр в мантиссе
                num = num.replace('e','0e');
            else
                if (num.length > text_max && mantissa > 1)   //Если длина строки больше заданной и есть куда сжимать мантиссу
                    num = num.replace('0e','e');
            //Ещё пример, для числа 0.00999999 была рассчитана длина мантиссы 2, и, следовательно, ожидалась строка «9.99e-3».
            //Однако метод toExponential округляет число и выдаёт строку «1.00e-2». На мой взгляд, это число выглядит приятней в виде «0.01000».
            //Следующие две строчки кода устраняют этот недостаток.
            if (num.slice(-3) === "e-2")
                num = (num.slice(0, 2) + '0.01').padEnd(text_max, '0')
        }
        else {
            num += x.toString();
            if (!Number.isInteger(x))
                num = num.substring(0, text_max).padEnd(text_max, '0');
            else
                if (float_type)
                    num += '.';
        }
    }
    return num.padStart(text_max, ' ');
}

function genId(){
    return ++tensor._id;
}
tensor._id = 0;
tensor.cos_similar = (A, B) => {
    if (A && B) {
        A = A.data || A;
        B = B.data || B;
        let scalar = 0;
        let avgA = 0;
        let avgB = 0;
        let a, b
        let i = A.length;
        while (i--){
            a = A[i];
            b = B[i];
            scalar += a * b;
            avgA += a * a;
            avgB += b * b;
        }
        if(scalar){
            avgA = Math.sqrt(avgA);
            avgB = Math.sqrt(avgB);
            scalar /= avgA * avgB;
            return scalar;//Math.abs(scalar);
        }
    }
    return 0;
}

tensor.rearrange = (expr, src)=>{
    //todo
}
tensor.reduce = (expr, src, agg_func = 'max')=>{
    //todo
}
tensor.repeat = (expr, src, vars = {})=>{
    //todo
}
tensor.pack = (expr, inputs)=>{
    //todo
}
tensor.unpack = (expr, inputs)=>{
    //todo
}

torus.STEP = 0;
systems:{
    torus.get_broadcast_shapes = (...tensors)=>{
        return tensors.reduce((r, t, n)=>{
            t.shape.toReversed().forEach((d, i)=>{
                let d_r = r[i] ??= d;
                if (d_r !== d){
                    if (d_r !== 1 && d !== 1)
                        throw new Error(`Broadcast error for tensor ${n} `);
                    r[i] = Math.max(d, d_r);
                }
            })
            return r;
        },[]).toReversed();
    }
    torus.get_out = function (place, key = 'out' ){
        if (!torus.USE_GET_OUT)
            return;
        if (Array.isArray(place)){
            place = place.find(p=>p.OUTS);
        }
        return place?.OUTS?.[key];
    }
    torus.set_out = function (place, out, key = 'out'){
        if (!torus.USE_GET_OUT)
            return;
        if (Array.isArray(place)){
            place = place.find(p=>p.OUTS);
        }
        if(place?.OUTS && out){
            out.OUTS ??= Object.create(null);
            place.OUTS[key] = out;
        }

    }

    torus.compare_shapes = (...tensors)=>{
        tensors = torus.flat(tensors);
        const shapes = tensors.filter(Boolean).map(t=>t.shape.toReversed());
        if (shapes.length > 1) {
            const max_dim = shapes.reduce((res, shape)=>{
                return Math.max(res, shape.length);
            }, 0);
            const max_shape = Array(max_dim).fill(1);
            shapes.forEach((shape, idx)=>{
                shape.forEach((dim, i)=>{
                    if (dim === 1) return;
                    if (max_shape[i] === 1)
                        max_shape[i] = dim;
                    else
                        if (dim !== max_shape[i]) {
                            //Для выдачи подробной ошибки необходимо найти с кем конкретно конфликтует текущий тензор
                            for (let j=0; j<shapes.length; j++)
                                if (shapes[j][i] && shapes[j][i]!==1 && shapes[j][i]!==dim)
                                    throw new Error(`compare_shapes(): ` +
                                        `The shape (${shapes[j].toReversed()}) of tensor ${j} mismatches the shape (${shape.toReversed()}) of tensor ${idx} ` +
                                        `at non-singleton dimension`);
                        }
                });
            });
        }
        return shapes.map(shape=>shape.mul() || 1);
    }
    torus.compare_shapes_strict = (...tensors)=>{
        tensors = torus.flat(tensors);
        const shapes = tensors.filter(Boolean).map(tensor => tensor.shape.length? tensor.shape: [1]);   // Учитываем, что скаляры имеют 'пустую' форму []
        if (shapes.length > 1) {
            const reference_shape = shapes[0];
            const reference_dim = reference_shape.length;
            shapes.forEach((shape, idx)=>{
                if (shape.length !== reference_dim || shape.some((dim, i) => dim !== reference_shape[i]))
                    throw new Error(`compare_shapes_strict(): Tensor ${idx} has shape (${shape}) ` +
                                    `that mismatches the shape (${reference_shape}) of tensor 0`);
            });
        }
        return shapes[0]?.mul() || 1;
    }    
    torus.compare_shapes_except_dim = (tensors, dim)=>{
        tensors = torus.flat(tensors);
        const shapes = tensors.map(tensor => tensor.shape.length? tensor.shape: [1]);   // Учитываем, что скаляры имеют 'пустую' форму []
        const reference_shape = [...shapes[0]];
        if (shapes.length > 1) {
            dim = tensors[0].check_dim(dim);
            const reference_dim = reference_shape.length;
            reference_shape[dim] = shapes.reduce((r, shape, idx)=>{
                    if (shape.length !== reference_dim)
                        throw new Error(`compare_shapes_except_dim(dim=${dim}): Tensor ${idx} has shape ${shape.length}-D ` +
                                        `that mismatches the shape ${reference_dim}-D of tensor 0`);
                    if (shape.some((d, i) => d !== reference_shape[i] && i !== dim) )
                        throw new Error(`compare_shapes_except_dim(dim=${dim}): Tensor ${idx} has shape (${shape}) ` +
                                        `that mismatches the shape (${reference_shape}) of tensor 0`);
                    return r + shape[dim];
            }, 0);
        }
        return reference_shape;
    }    

    torus._shapes_are_equal = (...tensors)=>{
        tensors = torus.flat(tensors);
        const shapes = tensors.filter(Boolean).map(t=>t.shape);
        if (shapes.length > 1) {
            const reference_shape = shapes[0];
            const reference_dim = reference_shape.length;
            return shapes.every((shape) => shape.length === reference_dim && shape.every((dim, i) => dim === reference_shape[i]) );
        }
        return true;
    }
    torus._shapes_are_equal_except_dims = (tensors, dims)=>{
        tensors = torus.flat(tensors);
        const shapes = tensors.map(tensor => tensor.shape.length? tensor.shape: [1]);   // учитываем, что скаляры имеют 'пустую' форму []
        if (shapes.length > 1) {
            const reference_shape = shapes[0];
            const reference_dim = reference_shape.length;
            dims = dims.map(dim => dim<0? dim + reference_shape.length: dim);
            return shapes.every((shape) => shape.length === reference_dim && shape.every((dim, i) => dim === reference_shape[i] || dims.includes(i)) );
        }
        return true;
    }
    torus._shapes_are_compatible = (...tensors)=>{  // Тензоры совместимы для некоторых расчётов, если одноименные оси имеют одинаковый размер.
                                                    // Размер 1 совместим с любым размером. Если тензор не имеет оси, то её размер считается равным 1.
        tensors = torus.flat(tensors);
        const shapes = tensors.filter(Boolean).map(t=>t.shape.toReversed());
        if (shapes.length > 1) {
            const max_dim = shapes.reduce((res, shape)=>{
                return Math.max(res, shape.length);
            }, 0);
            const max_shape = Array(max_dim).fill(1);
            return shapes.every((shape)=>{
                return shape.every((dim, i)=>{
                    if (max_shape[i] === 1)
                        max_shape[i] = dim;
                    return dim === 1 || dim === max_shape[i];
                });
            });
        }
        return true;
    }
    torus.prototype.shape_is_compatible_by_size = function (...shape_or_tensor) {   // Проверяет возможность приведения тензора к заданной форме.
                                                                                    // В качестве формы может быть указан тензор или массив измерений.
                                                                                    // В массиве одно из измерений может иметь значение -1, в этом случае оно рассчитывается.
        let shape = torus.flat(shape_or_tensor);
        if (Object.equal(shape[0]?.constructor, tensor))
            shape = shape[0].shape;
        else {    //Проверяем наличие осей с неизвестным размером
            const known_axes = shape.filter((v)=> v!==-1);
            if (shape.length - known_axes.length !== 0)   //Если существуют оси с неизвестным размером
                if (shape.length - known_axes.length === 1)    //Если такая ось единственная, рассчитываем размер оси
                    shape[shape.indexOf(-1)] = this.size / (known_axes.mul() || 1);
                else    //Если неизвестны несколько осей
                    return false;
        }
        if (shape.some((v)=> !Number.isInteger(v) || v<0))
            return false;
        const size = shape.mul() || 1;
        if (size !== this.size)
            return false;
        return true;
    }
    torus._common_dType = (...tensors)=>{
        tensors = torus.flat(tensors);
        const dTypes = tensors.filter(Boolean).map(t=> Object({type: t.dType.name[0], size: +t.dType.name.match(/\d+/)[0]}) );
        const fn = function(a, b) {
            const dType = {};
            if (a.type === b.type) {   //Если базовые типы одинаковые, берём наибольший размер
                dType.type = a.type;
                dType.size = Math.max(a.size, b.size);
            }
            else if (a.type === 'F' || b.type === 'F') {   //Если один из типов Float, то выбираем Float и берём его размер
                dType.type = 'F';
                dType.size = a.type === 'F'? a.size: b.size;
            }
            else {   //Остались: один из типов Int, другой Uint
                if (a.type === 'U')
                    [a, b] = [b, a];
                if (b.size === 32) {   //Если Uint имеет максимальный размер
                    dType.type = 'F';
                    dType.size = 32;
                }
                else {
                    dType.type = 'I';
                    dType.size = Math.max(a.size, b.size * 2);
                }
            }
            return dType;
        };
        const cType = dTypes.reduce(fn, dTypes[0]);
        switch(cType.type + cType.size) {
            case 'I8':  return Int8Array;
            case 'I16': return Int16Array;
            case 'I32': return Int32Array;
            case 'U8':  return Uint8Array;
            case 'U16': return Uint16Array;
            case 'U32': return Uint32Array;
            case 'F32': return Float32Array;
        }
        return torus.DEFAULT_TYPE;
    }    
    torus._check_list_of_tensors = (...tensors)=>{
        tensors = torus.flat(tensors);
        if (tensors.length === 0 || tensors.length === 1 && tensors[0] === undefined)
            throw new Error(`_check_list_of_tensors(): expected a non-empty TensorList`);
        const idx = tensors.findIndex(t => t?.constructor.name !== 'tensor');
        if (idx !== -1)
            throw new Error(`_check_list_of_tensors(): expected Tensor as element ${idx} in TensorList, but got ${tensors[idx]?.constructor.name||tensors[idx]}`);
        return tensors;
    }
    torus.prototype.check_dim = function (dim, extendable = false){  // extendable = false -- для текущего числа измерений, true -- предполагается увеличение числа измерений
        if (!Number.isInteger(dim))
            throw new Error(`tensor.check_dim((dim = ${dim}): argument 'dim' must be Integer, but got ${typeof dim === 'number'? dim: dim?.constructor.name||dim}`);
        const tensor_dim = this.dim + Number(extendable);
        if (dim < -tensor_dim || dim >= tensor_dim)
            throw new Error(`tensor.check_dim((dim = ${dim}): dimension out of range (expected to be in range of [-${tensor_dim}, ${tensor_dim - 1}], but got ${dim})`);
        if (dim < 0)
            dim += tensor_dim;
        return dim;
    }
    torus.prototype.check_shape = function (...shape) {   // shape or tensor
        shape = torus.flat(shape);
        if (Object.equal(shape[0]?.constructor, tensor))
            shape = shape[0].shape;
        else {    //Проверяем наличие осей с неизвестным размером
            const known_axes = shape.filter((v)=> v!==-1);
            if (shape.length - known_axes.length !== 0)   //Если существуют оси с неизвестным размером
                if (shape.length - known_axes.length === 1)    //Если такая ось единственная, рассчитываем размер оси
                    shape[shape.indexOf(-1)] = this.size / (known_axes.mul() || 1);
                else    //Если неизвестны несколько осей
                    throw new Error(`tensor.check_shape(${shape}): only one dimension can be inferred`);
        }
        if (shape.some((v)=> !Number.isInteger(v) || v<0))
            throw new Error(`tensor.check_shape(${shape}): dimensions must be positive integer`);
        const size = shape.mul() || 1;
        if (size !== this.size)
            throw new Error(`tensor.check_shape(${shape}): convert (${this.shape}) to (${shape}) not allow`);
        return shape;
    }
    torus.async = (handler)=>{
        return new Promise(resolve=>{
            setTimeout(()=>{
                handler();
                resolve()
            })
        })
    }
    torus.prototype.gen_chars = function(dims = []){
        dims = torus.flat(dims);
        let chars = this.shape.map((_, i)=>{
            return String.fromCharCode(i + 97);
        }).toReversed();
        if(dims.length){
            dims = dims.map(d=>{
                return this.check_dim(d);
            })
            chars = chars.map((ch,d)=>{
                if(dims.includes(d))
                    return ch;

            }).filter(Boolean)
        }
        return chars;
    }
    Object.defineProperty(torus.prototype, 'shape_info', {
        configurable: true,
        get(){
            return this['#shape_info'] ??= (()=>{
                let stride, c, m = 1;
                return this.shape.toReversed().map((size, idx)=>{
                    stride = m;
                    m *= size;
                    let char = String.fromCharCode(idx + 97);
                    idx = this.dim - idx - 1;
                    return {stride, size, char, idx};
                }).toReversed();
            })()
        }
    })
    Object.defineProperty(torus.prototype, 'strides', {
        configurable: true,
        get(){
            let m = 1;
            return this.shape.toReversed().map((dim)=>{
                let s = m;
                m *= dim;
                return s;
            }).toReversed();
        }
    })
    torus.prototype.dim_info = function(...dims){
        dims = torus.flat(dims).filter(v=> v || v === 0);
        // dims = torus.flat(dims).filter(Boolean);
        let shape_info = this.shape_info;
        if (dims.length){
            shape_info = dims.reduce((r, d, idx)=>{
                idx = this.check_dim(d);
                let v = shape_info[idx];
                if (v)
                    r.add(v)
                return r
            }, []).sort((a,b)=>{
                return a.idx<b.idx?-1:1
            })
        }
        return shape_info;
    }
    torus.prototype.fill_ = function(value_or_handler = 0) {
        if (typeof value_or_handler === 'function') {
            const tmp = this.data.map(value_or_handler);
            this.data.set(tmp);
            tmp.buffer.transfer(0);
        }
        else
            this.data.fill(value_or_handler);
        return this;
    }
    torus.fill = (shape, value_or_handler, $ = {}) => {
        $ = torus.$($)
        shape = torus.flat(shape);
        const size = shape.mul() || 1;
        if ($.dType === 'auto')
            if (typeof value_or_handler === 'function' )
                $.dType = torus.DEFAULT_TYPE;
            else
                $.dType = torus._detect_dType(value_or_handler);
        let out =  torus.from(new $.dType(size))._shape(shape)._label(torus.label_from_error());
        return out.fill_(value_or_handler);
    }

    torus._element_wise = function ($ = {forward: '', backward_0: '', backward_1: '', label: ''}, ...tensors){
        tensors = torus.flat(tensors);
        tensors = tensors.map(t=>torus.from(t));
        let out = torus.get_out(this, $.forward);
        if (!out){
            let shape = torus.get_broadcast_shapes(...tensors);
            out = torus.from(new torus.DEFAULT_TYPE(shape.mul() || 1))._shape(shape)._src(tensors);
            torus.set_out(this, out, $.forward);
            shape = shape.toReversed();
            let shapes = tensors.map(t=>t.shape.toReversed());

            const code_generator = (to_back = false)=>{
                let code = /*javascript*/`
${to_back?
    'let out_grad = out.grad.data;\n' +
    'let fn_back = [];\n' +
    tensors.map((t, i)=> t.allowGrad? `fn_back[${i}] = ${$['backward_'+i] || $['backward']};`: '').filter(Boolean).join('\n')
:
    'let out_data = out.data;\n' +
    `let fn = ${$.forward};`
}
let datas = tensors.map(t=>t.data);
${to_back? 'let grads = tensors.map(t=>t.allowGrad?t.grad.data:null);\n': ''}
let cnt = 0;
${
                    shape.map((d, i)=>{
                        if (!shapes.some(s=> s[i] > 1))
                            return   ''
                        return '  '.repeat((shape.length - i - 1)) + `for(let ${
                            shapes.reduce((r, s, t)=>{
                                if (s[i] > 1){
                                    r.push(`i${i}t${t} = 0`)
                                }
                                return r;
                            }, []).join(', ')
                        }; ${
                            shapes.reduce((r, s, t)=>{
                                if (s[i] > 1){
                                    r.push(`i${i}t${t} < ${s.filter((_,j)=>j<=i).mul()}`)
                                }
                                return r;
                            }, [])[0]
                        }; ${
                            shapes.reduce((r, s, t)=>{
                                if (s[i] > 1){
                                    r.push(`i${i}t${t} += ${s.filter((_,j)=>j<i).mul() || 1}`)
                                }
                                return r;
                            }, []).join(', ')
                        }){`}).toReversed().join('\n')} 
${
                    shapes.map((s, i)=>{
                        return '  '.repeat(shape.length)+`let idx${i} = ${
                            s.reduce((r, d, j)=>{
                                if (d > 1){
                                    r.push(`i${j}t${i}`)
                                }
                                return r;
                            },[]).join(' + ') || 0
                        };`
                    }).join('\n') + (to_back?`
${
                            '  '.repeat(shape.length)+`let vals = [];\n` +
                            shapes.map((s, i)=>{
                                return '  '.repeat(shape.length)+`vals[${i}] = datas[${i}][idx${i}];`
                            }).join('\n') + '\n' +
                            '  '.repeat(shape.length)+'let grad = out_grad[cnt++];\n' +
                            tensors.map((t, i)=> t.allowGrad? ('  '.repeat(shape.length)+`grads[${i}][idx${i}] += grad * fn_back[${i}](...vals);`): '').filter(Boolean).join('\n')
                        }`
                        :`
${
                            '  '.repeat(shape.length)}out_data[cnt++] = fn(${
                            shapes.map((s, i)=>{
                                return `datas[${i}][idx${i}]`;
                            })
                        });`)

                }
${
                    shape.map((_, i)=>{
                        if(!shapes.some(s=>s[i]>1))
                            return   ''
                        return '  '.repeat(i) + '}'

                    }).toReversed().join('\n')}    
return out;
`;
                return code;

            }
            let code = code_generator();
            out._fwd = new Function('out', 'tensors', code);
            if (out.src.some(t=>t.allowGrad)){
                code = code_generator(true);
                let back_fn = new Function('out', 'tensors', code);
                out._back = ()=>{
                    return back_fn(out, out.src)
                }
            }
        }
        return out._fwd(out, tensors)._src(tensors)
    }

    //Определяет оптимальный тип буфера для хранения арифметической прогрессии, формируемой с заданным шагом.
    //Учитывает, что конечное значение диапазона в прогрессию не входит, как и во всех остальных методах. Но если step===0, то входит.
    torus._detect_dType = function(from, to, step) {
        if (to === undefined) {   //Если тип определяется для одного числа
            to = from;
            step = 0;
        }
        else {  //Если тип определяется для диапазона чисел
            if (step === undefined)   //Если шаг не задан
                step = Math.sign(to - from);
            if (Math.abs(to - from) < Math.abs(step))
                to = from;
            else
                to -= step;
        }
        const from_is_Integer = Number.isInteger(from);
        if (from > to) {   //Начало диапазона делаем меньше конца, чтобы дальше было проще сравнивать с границами числовых типов
            [from, to] = [to, from];
        }
        if ( from_is_Integer && Number.isInteger(step) ) {   //Если ожидаются целочисленные значения
            if( from>=-(2**(Int8Array.BYTES_PER_ELEMENT*8-1)) && to<2**(Int8Array.BYTES_PER_ELEMENT*8-1) )   //-128 ... 127
                return Int8Array;
            if( from>=0 && to<2**(Uint8Array.BYTES_PER_ELEMENT*8))   // 0 ... 255
                return Uint8Array;
            if( from>=-(2**(Int16Array.BYTES_PER_ELEMENT*8-1)) && to<2**(Int16Array.BYTES_PER_ELEMENT*8-1))   //-32768 ... 32767
                return Int16Array;
            if( from>=0 && to<2**(Uint16Array.BYTES_PER_ELEMENT*8))   // 0 ... 65535
                return Uint16Array;
            if( from>=-(2**(Int32Array.BYTES_PER_ELEMENT*8-1)) && to<2**(Int32Array.BYTES_PER_ELEMENT*8-1))   //-2147483648 ... 2147483647
                return Int32Array;
            if( from>=0 && to<2**(Uint32Array.BYTES_PER_ELEMENT*8))   // 0 ... 4294967295
                return Uint32Array;
        }
        const max_float32 = 0b111111111111111111111111 * 2**(127-23);   // 3.4028234663852886e+38
        const min_float32 = 2**(-149);   // 1.401298464324817e-45
        if( from >= -max_float32 && to <= max_float32)   //Попадает в диапазон Float32Array
            //Попадает в минимальные значения Float32Array
            if( (Math.abs(from)>=min_float32 || from===0) && (Math.abs(to)>=min_float32 || to===0) && (Math.abs(step)>=min_float32 || step===0) )
                return Float32Array;
        return torus.DEFAULT_TYPE;
    }
}
einops:{

    function parseEinsumFormula(formula, tensors) {
        let [inputTerms, secondPart] = formula.split('->');
        let inputs =  inputTerms.split(',');
        inputs = inputs.map(term=>{
            term = term.trim();
            while(term.includes('..'))
                term = term.replace('..', '.');
            return term.split('');
        });

        let [outputTerm, varsPart] = secondPart ? secondPart.split(':').map(term=>term.trim()) : [];
        if(!outputTerm)
            outputTerm = '';
        outputTerm = outputTerm.trim();
        while(outputTerm.includes('..'))
            outputTerm = outputTerm.replace('..', '.')

        let outputs = outputTerm.split('');
        const vars = varsPart ? varsPart.trim().split(',').reduce((r, v)=>{
            v = v.trim().split('=');
            r[v[0].trim()] = +v[1].trim();
            return r;
        }, Object.create(null)): Object.create(null);

        const varsIndices = Object.keys(vars);

        let allIndices = inputs.flat().reduce((r,idx)=>{
            if(idx !== '.' && !r.includes(idx))
                r.add(idx);
            return r
        }, []);

        //проверки

        if(!inputs.length)
            throw new Error(`The expression "${formula}" must contain at least one input tensor.`);
        let dots = [];
        inputs = inputs.map((input, i)=>{
            // if(input.length === 0)
            //     throw new Error(`Input #${i+1} in expression "${formula}" must contain at least one input index.`);
            let uniq = input.reduce((r, idx, j)=>{
                if(idx === '.' && j)
                    throw new Error(`Input #${i+1} in expression "${formula}" must use mask "..." only before other indices.`);
                if(r.includes(idx))
                    throw new Error(`Input #${i+1} in expression "${formula}" contains a repeating index "${idx}".`);
                r.push(idx);
                return r;
            }, [])
            let tensor = tensors[i];
            let shape_info = tensor.shape_info.toReversed();
            input = input.toReversed();
            input = shape_info.map((info, d)=>{
                let idx = input[d];
                if(!idx || idx === '.'){
                    if(input[input.length-1] !== '.')
                        throw new Error(`Input #${i+1} in expression "${formula}" must match the dimension of the tensor.`);
                    for(let c = 97; c<255; c++ ){
                        idx = String.fromCharCode(c);
                        if(!allIndices.includes(idx))// && !dots.includes(idx))
                            break;
                    }
                    dots.push(idx)
                    allIndices.push(idx)
                }
                return {idx, size: info.size, stride: info.stride};
            })
            return input.toReversed();
        })
        let flat_inputs = inputs.flat()
        flat_inputs.forEach((axis, i)=>{
            let idx = axis.idx;
            let size;
            flat_inputs.filter(a=>a.idx === idx).forEach(a=>{
                if(!size)
                    size = a.size
                if(a.size === size || a.size === 1)
                    return;
                throw new Error(`Input #${i+1} in expression "${formula}" has incorrect index ${idx}.`)
            })
        })
        outputs.forEach((idx, i)=>{
            if(idx === '.' &&  !i && dots.length){
                outputs.shift();
                outputs.unshift(...dots.toReversed())
                return;
            }
            if(!allIndices.includes(idx) && !varsIndices.includes(idx) && !dots.includes(idx))
                throw new Error(`Output index "${idx}" in expression "${formula}" must be alsow define in inputs or variables.`)
            if(idx === '.' && i)
                throw new Error(`Output in expression "${formula}" must use mask "..." only before other indices.`);
        })
        // if(dots.length && outputs[0] === '.'){
        //     outputs.shift();
        //     outputs.unshift(...dots.toReversed())
        // }
        varsIndices.forEach(idx=>{
            if(!outputs.includes(idx))
                throw new Error(`Variable index "${idx}" in expression "${formula}" must be define in output.`);
            if(inputs.flat().includes(idx))
                throw new Error(`Variable index "${idx}" in expression "${formula}" contains index already used in inputs.`);
        })

        let stride, m = 1;
        outputs = outputs.toReversed().map(idx=>{
            stride = m;
            let size = flat_inputs.filter(axis=>axis.idx === idx).sort((a,b)=>a.size>b.size?1:-1)[0]?.size;
            if(!size)
                size = vars[idx];
            m *= size;
            return  {idx, size, stride};
        }).toReversed();
        return{
            allIndices,
            inputs,
            outputs,
            vars
        }
    }
    //GPU EINSUM
    torus.einsum = function (expression, tensors = [], grad){

        tensors = torus.flat(tensors).map(t=>torus.from(t));
        let key = expression + ': ' + tensors.map(t=>t.shape.toString()).join(',');
        let out = torus.get_out(tensors, key);
        if (!out){
            const model = parseEinsumFormula(expression, tensors);
            let shape  = model.outputs.map(s=>s.size);
            if(grad)
                out = grad;
            else
                out = torus.from(new Float32Array(shape.mul() || 1))._src(tensors)._label(`einsum('${expression}')`)._shape(shape);
            torus.set_out(tensors, out, key);
            if(torus.USE_GPU && tensors.some(t=>t.gpuDataBuffer)){

                out.writeToGPU();
                let work_groups = [];

                switch (out.shape.length){
                    case 0:{
                        work_groups = [1];
                    } break;
                    case 1:{
                        work_groups = [Math.min(256, out.shape[0])];
                    } break;
                    case 2:{
                        work_groups = [Math.min(16, out.shape[0]), Math.min(16, out.shape[1])];
                    } break;
                    default:{
                        work_groups = [Math.min(8, out.shape[0]), Math.min(8, out.shape[1]), Math.min(4, out.shape[2])];
                    } break;
                }
                let uniq_tnsors = tensors.reduce((r, t)=>{
                    if(!r.includes(t))
                        r.push(t);
                    return r;
                },[])
                let conv_indices = model.allIndices.filter(idx=>!model.outputs.find(ax=>ax.idx === idx));
                let cb = new CodeBuilder(
                    uniq_tnsors.map((t,i)=>{
                        return `@group(0) @binding(${i}) var <storage, read> t${t.allowGrad?t.id:('mp'+i)}: array<${t.gpuType}>;`
                    }).join('\n'),
                    `@group(0) @binding(${uniq_tnsors.length}) var <storage, read_write> t${out.id}: array<f32>;`,
                    `@compute @workgroup_size(${work_groups})`,
                    `fn main(@builtin(global_invocation_id) global_id: vec3<u32>){`,
                    model.outputs.map((axis, i)=>{
                        switch(i){
                            case 0:
                                return `    let ${axis.idx} = global_id.x;`
                            case 1:
                                return `    let ${axis.idx} = global_id.y;`
                            case 2:
                                return `    let flatIndex = global_id.z;`

                        }
                    }).filter(Boolean).join('\n'),

                    (()=>{
                        let expr = [];
                        for(let i = 2; i<model.outputs.length; i++){
                            let axis = model.outputs[i];
                            expr.push(`    let ${axis.idx} = (flatIndex / (${model.outputs.filter((_, j)=>j>i).map(ax=>ax.size).join('*') || 1})) % ${axis.size};`)
                        }
                        return expr.join('\n');
                    })(),



                    (()=>{
                        let expr = model.outputs.map((axis, i)=>`${axis.idx}>=${axis.size}`).join(' || ');
                        if(expr)
                            return '    if(' + expr + ') {return;}';
                        return ''
                    })(),

                    '    var sum: f32 = 0.0;',

                    (()=>{
                        let f = [];
                        for(let i = 0; i<conv_indices.length; i++){
                            let idx = conv_indices[i];
                            let axis = model.inputs.flat().find(ax=>ax.idx === idx);
                            if(axis)
                                f.push(' '.repeat(i * 4) + `    for(var ${axis.idx}: u32 = 0; ${axis.idx} < ${axis.size}; ${axis.idx}++){`)
                        }
                        return f.join('\n')
                    })(),

                    (()=>{
                        return model.inputs.map((input, i)=>{
                            return ' '.repeat(conv_indices.length * 4) + `    let idx${i} = ${
                                input.map(info=>info.idx + ' * ' + info.stride).join(' + ') || 0
                            };`;
                        }).join('\n');
                    })(),


                    ' '.repeat(conv_indices.length * 4) + `    sum += ${
                        (()=>{
                            return tensors.map((t, i)=>`t${t.allowGrad?t.id:('mp'+i)}[idx${i}]`).join(' * ')
                        })()
                    };`,

                    (()=>{
                        let f = [];
                        for(let i = 0; i<conv_indices.length; i++){
                            let idx = conv_indices[i];
                            let axis = model.inputs.flat().find(ax=>ax.idx === idx);
                            if(axis)
                                f.push(' '.repeat(i * 4) + `    }`)
                        }
                        return f.toReversed().join('\n')
                    })(),

                    '    ' + `t${out.id}[${
                        (()=>{
                            return model.outputs.map(axis=>axis.idx + (axis.stride>1?` * ${axis.stride}`:'')).join(' + ') || 0;
                        })()
                    }] ${grad?'+':''}= sum;`,

                    `}`,
                )

                let code = cb.code;
// >code
                for(let i = 0; i<3; i++){
                    let info = model.outputs[i];
                    if(info && work_groups[i])
                        work_groups[i] = Math.ceil(info.size / work_groups[i]);
                }
                // if(model.outputs.length>2)
                //     work_groups[2] = Math.ceil(model.outputs.filter((_,i)=>i>1).reduce((r,ax)=>r*ax.size,1) / work_groups[2])

                out._fwd = (tensors)=>{
                    let tmp_tensors = tensors.filter(t=>!t.allowGrad);
                    tmp_tensors.forEach((t,i)=>t.writeToGPU());
                    torus.WebGPU.run(code, [...uniq_tnsors, out], work_groups);
                    tmp_tensors.forEach(t=>t.gpuDestroy())
                    return out._src(tensors);
                }
            }
            else{
                let cb = new CodeBuilder(
                    tensors.map((tensor, i)=>`let data_${i} = t[${i}].data;`).join('\n'),
                    `let out_data = out.data;`,
                    model.outputs.map((out, i)=>{
                        return ' '.repeat(i * 4) + `for(let o_${out.idx} = 0${
                            (()=>{
                                let s = model.inputs.map((input, ii)=>{
                                    let info = input.find(axis=>axis.idx === out.idx);
                                    if(info){
                                        return 'i_'+out.idx+ii+' = 0'
                                    }
                                }).filter(Boolean).join(', ');
                                if(s)
                                    s = ', ' + s
                                return s
                            })()
                        }; o_${out.idx} < ${out.size * out.stride}; o_${out.idx} += ${out.stride}${
                            (()=>{
                                let s = model.inputs.map((input, ii)=>{
                                    let info = input.find(axis=>axis.idx === out.idx);
                                    if(info){
                                        let del_idx = model.allIndices.indexOf(out.idx);
                                        model.allIndices.splice(del_idx, 1);
                                        return 'i_'+out.idx+ii+' += ' + info.stride;
                                    }
                                }).filter(Boolean).join(', ');
                                if(s)
                                    s = ', ' + s
                                return s
                            })()
                        }){`
                    }).join('\n'),
                    ' '.repeat(model.outputs.length * 4) + 'let sum = 0;',

                    (()=>{
                        return model.allIndices.map((idx, i)=>{
                            return ' '.repeat((i + model.outputs.length)  * 4) + `for(let ${
                                (()=>{
                                    return model.inputs.map((input, ii)=>{
                                        let info = input.find(axis=>axis.idx === idx);
                                        if(info){
                                            return 'i_'+idx+ii+' = 0'
                                        }
                                    }).filter(Boolean).join(', ');
                                })()
                            }; ${
                                (()=>{
                                    return model.inputs.map((input, ii)=>{
                                        let info = input.find(axis=>axis.idx === idx);
                                        if(info){
                                            return 'i_'+idx+ii+' < '+(info.size * info.stride);
                                        }
                                    }).filter(Boolean)[0];
                                })()
                            }; ${
                                (()=>{
                                    return model.inputs.map((input, ii)=>{
                                        let info = input.find(axis=>axis.idx === idx);
                                        if(info){
                                            return 'i_'+idx+ii+' += '+ info.stride;
                                        }
                                    }).filter(Boolean).join(', ');
                                })()

                            }){`
                        }).join('\n')
                    })(),

                    ' '.repeat((model.allIndices.length + model.outputs.length) * 4) + `sum += ${
                        (()=>{
                            return model.inputs.map((input, i)=>`data_${i}[${
                                    input.map(axis=>'i_'+axis.idx+i).join(' + ')
                                }]`
                            ).join(' * ')
                        })()
                    };`,

                    (()=>{
                        return model.allIndices.map((idx, i)=>{
                            return ' '.repeat((i + model.outputs.length)  * 4) + `}`
                        }).toReversed().join('\n')
                    })(),


                    ' '.repeat(model.outputs.length * 4) + `out_data[${model.outputs.map(out=>`o_${out.idx}`).join(' + ') || 0}] = sum;`,
                    model.outputs.map((out, i)=>{
                        return ' '.repeat(i * 4) + '}';
                    }).toReversed().join('\n')

                )
                let code = cb.code;

                let fn = new Function('out', 't', code);
                out._fwd = (tensors)=>{
                    fn(out, tensors);
                    return out._src(tensors);
                }
            }
            if(!grad && tensors.some(t=>t.allowGrad)){
                out._back = ()=>{
                    let sources = out.src;
                    let grads = sources.map((t, i)=>{
                        if(!t.allowGrad) return;
                        let inputs = model.inputs.map((input, j)=>i !== j?input:model.outputs)
                        let output = model.inputs[i];
                        let expr = inputs.map(input=>input.map(ax=>ax.idx).join('')).join(',') + '->' + output.map(ax=>ax.idx).join('')
                        inputs = inputs.flat();
                        let vars = output.filter(ax=>!inputs.find(a=>a.idx === ax.idx));
                        if(vars.length)
                            expr += ': '+ vars.map(ax=>ax.idx + '=' + ax.size).join(', ');
                        let back_targets = sources.map((tensor, idx)=>(idx === i)?out.grad:tensor)
                        torus.einsum(expr, back_targets, t.grad);
                    })
                }
            }
        }
        return out._fwd(tensors);
    }
}
generators:{

// ФУНКЦИИ ГЕНЕРАТОРЫ

    torus.hippo = (size)=>{
        const data = new Float64rray(size * size);
        let x;
        for (let n=0, stride=0; n<size; n++, stride+=size) {
            data[stride+n] = -(n + 1);
            x = 4 * n + 2;
            let k = n;
            while( k-- )
                data[stride + k] = -Math.sqrt(x * (k + 0.5));
        }
        return torus.from(data)._shape(size, size)._label('hippo');
    }
    torus.randn = torus.rand_n = (...shape)=>{
        const handle = ()=>{
            return Math.sqrt(-2 * Math.log(torus.generator())) * Math.cos((2 * Math.PI) * torus.generator());
        }
        return torus.fill(shape, handle, {dType: torus.DEFAULT_TYPE})._label(`randn`);
    }
    torus.zeros_like = (pattern) => {
        return torus.zeros(pattern.shape);
    }
    torus.ones_like = (pattern) => {
        return torus.ones(pattern.shape);
    }
    torus.rand_n_like = (pattern) => {
        return torus.rand_n(pattern.shape);
    }

    torus.rand_int = (min_or_max = 0, max, ...shape)=>{
        shape = torus.flat(shape);
        if (max === undefined) {
            max = min_or_max;
            min_or_max = 0;
        }
        min_or_max = Math.trunc(min_or_max);
        max = Math.trunc(max);
        if (max <= min_or_max)
            throw new Error(`torus.rand_int(min_or_max = ${min_or_max}, max = ${max}, ...shape = [${shape}]): max <= min`);
        if (shape.length === 0)
            shape = [Math.round(max - min_or_max)];
        const data = new torus.DEFAULT_TYPE(shape.mul() || 1).map(i=>{
            const r = torus.generator();
            return Math.floor(r * (max - min_or_max) + min_or_max);
        });
        return new torus(data, {dType: torus.DEFAULT_TYPE})._shape(shape)._label(`rand_int ${min_or_max} … ${max}`);
    }
    // FINAL


    torus.arange = (from_or_size = 0, to, ...step_or_shape)=>{
        const it_is_shape = Array.isArray(step_or_shape[0]);   //Если передан массив, то это однозначно форма. Позволяет отличить шаг от 1D формы.
        step_or_shape = torus.flat(step_or_shape);
        let step, steps;
        let label = 'arange';
        if (to === undefined) {   //Если у метода один параметр — размер прогрессии
            to = from_or_size;
            from_or_size = 0;
            step = Math.sign(to);
            steps = Math.ceil(Math.abs(to));
        }
        else if (step_or_shape.length === 0) {   //Если у метода два параметра — начало и конец прогрессии
            step = Math.sign(to - from_or_size)
            steps = Math.ceil(Math.abs(to - from_or_size));
        }
        else if (step_or_shape.length === 1 && !it_is_shape) {   //Если указан шаг прогрессии
            step = step_or_shape[0];
            if (step === 0)
                throw new Error(`torus.arange(from = ${from_or_size}, to = ${to}, step = ${step_or_shape}): step must be nonzero`);
            if (Math.sign(step) !== Math.sign(to - from_or_size) && to !== from_or_size)
                throw new Error(`torus.arange(from = ${from_or_size}, to = ${to}, step = ${step_or_shape}): starting and final bounds inconsistent with step sign`);
            steps = Math.ceil(Math.abs( (to - from_or_size) / step ));
        }
        else {   //Если указана форма тензора
            steps = step_or_shape.mul();
            step = (to - from_or_size) / steps;
        }
        const dType = torus._detect_dType(from_or_size, to, step);
        const data = new dType(steps);
        if ( steps ) {
            for ( let i = 0, v = from_or_size; i < steps ; i++, v += step)
                data[i] = v;
            label += ` ${from_or_size} … ${to}`;
        }
        if (step_or_shape.length <= 1)   //Если форма тензора не задана или тензор одномерный
            return torus.from(data)._label(label);
        return torus.from(data)._shape(step_or_shape)._label(label);
    }

    torus.eye = (...shape)=>{
        shape = torus.flat(shape);
        if (shape.length === 1)   //Если указана только одна ось, то создаётся вторая того же размера. Тензор приводится к 2-D.
            shape[1] = shape[0];
        const columns = shape[shape.length - 1] ?? 0;
        const rows = shape[shape.length - 2] ?? 0;
        const steps = Math.min( rows, columns);
        const step = columns + 1;
        const repeat = shape.length < 3 ? 1: shape.slice(0, -2).mul();
        const stride = rows * columns;
        const data = new torus.DEFAULT_TYPE(shape.mul() || [1]);
        for (let i=0, target=0; i<repeat ; i++, target+=stride)  //Заполнение старших размерностей
            for (let j=0, idx=target; j<steps; j++, idx+=step)   //Заполнение матрицы
                data[idx] = 1;
        return torus.from(data)._shape(shape)._label('eye');
    }

    torus.ones = (...shape) => {
        shape = torus.flat(shape);
        let data = 1;
        let size = shape.mul();
        if(size)
            data = new torus.DEFAULT_TYPE(size).fill(1)
        else
            shape = [];
        return torus.from(data)._label('ones')._shape(shape);
    }
    torus.zeros = (...shape) => {
        shape = torus.flat(shape);

        let data = 0;
        let size = shape.mul();
        if(size)
            data = new torus.DEFAULT_TYPE(size)
        else
            shape = [];
        return torus.from(data)._label('zeros')._shape(shape);
    }
    torus.rand_init = (shape, amplitude  = 1)=>{
        shape = torus.flat(shape);
        let size = shape.mul();
        if(!size){
            size = 1;
            shape = [1]
        }
        const data = new torus.DEFAULT_TYPE(size);
        function func(){
            return (torus.generator() - .5) * amplitude;
        }
        while(size--)
            data[size] = func();

        return torus.from(data)._label('rand_init')._shape(shape);
    }
    torus.empty = (...shape)=>{
        shape = torus.flat(shape);
        let size = shape.mul();
        if(!size){
            size = 1;
            shape = [1]
        }
        const data = new torus.DEFAULT_TYPE(size);
        while(size--)
            data[size] = torus.generator() - .5
        return torus.from(data)._label('empty')._shape(shape);
    }
    torus.rand = (shape, amplitude = 1, type = Float32Array) => {
        shape = torus.flat(shape);
        let size = shape.mul();
        if(!size){
            size = 1;
            shape = [1]
        }
        const data = new type(size);
        while(size--)
            data[size] = (torus.generator() - .5) * amplitude;
        return torus.from(data)._label('rand')._shape(shape);
    }
}
functions:{
    torus.prototype.expand = function(dim = -1, repeat = 1){
        if(Array.isArray(dim)){
            return dim.reduce((res, d, i)=>{
                return this.expand(d, Array.isArray(repeat)?(repeat[i] || 1):repeat);
            }, this)
        }
        dim = this.check_dim(dim);
        let shape = [...this.shape];
        shape[dim] = repeat;
        let other = torus.zeros(shape).float();
        let res = torus.concat([this, other], dim);
        this.shape[dim] += repeat;
        this.data = res.data;
        this.OUTS = Object.create(null);
        return this;
    }
    torus.prototype.sqrt = function(){
        let out = torus.get_out(this, 'sqrt');
        if (!out){
            out = torus.from(new torus.DEFAULT_TYPE(this.size))._shape(this)._src(this)._label('sqrt: ' + this.shape);
            torus.set_out(this, out, 'sqrt');
            out._fwd = ()=>{
                for (let i = 0; i<this.size; i++)
                    out.data[i] = Math.sqrt(this.data[i]);
                return out;
            }
            out._back = ()=>{
                for (let i = 0; i<this.size; i++){
                    this.grad.data[i] += out.grad.data[i] / (2 * out.data[i]);
                }
            }
        }
        return out._fwd();
    }
    torus.prototype.rsqrt = function(){
        return this.sqrt().pow(-1)._label('rsqrt: ' + this.shape)._label('rsqrt: ' + this.shape);
    }
    torus.prototype.invert = function (){
        return torus._element_wise.call(this, {forward: 'x => -x', backward_0: '() => -1'}, this)._label('invert: ' + this.shape);
    }
    torus.prototype.exp = function (){
        let out = torus.get_out(this, 'exp');
        if (!out){
            out = torus.from(new torus.DEFAULT_TYPE(this.size))._shape(this)._src(this)._label('exp: ' + this.shape);
            torus.set_out(this, out, 'exp');
            out._fwd = ()=>{
                for (let i = 0; i<this.size; i++)
                    out.data[i] = Math.exp(this.data[i]);
                return out;
            }
            out._back = ()=>{
                for (let i = 0; i<this.size; i++){
                    this.grad.data[i] += out.grad.data[i] * out.data[i];
                }
            }
        }
        return out._fwd();
    }
    torus.prototype.log = function (){
        return torus._element_wise.call(this, {forward: 'Math.log', backward_0: 'x => 1 / x'}, this)._label('log: ' + this.shape);
    }
    torus.prototype.tanh = function (){
        return torus._element_wise.call(this, {forward: 'Math.tanh', backward_0: 'x => (1 - x ** 2)'}, this)._label('tanh: '+ this.shape);
    }
    torus.prototype.sigmoid = torus.prototype.sigm = function (params){
        let out = torus.get_out(this, 'sigmoid');
        if (!out){
            out = torus.from(new torus.DEFAULT_TYPE(this.size))._shape(this)._src(this)._label('sigmoid: ' + this.shape);
            torus.set_out(this, out, 'sigmoid');
            if(this.gpuDataBuffer){
                out.writeToGPU();
                let workGroups =   Math.ceil(out.gpuDataBuffer.size / 256);
                let cb = new CodeBuilder(
                    `@group(0) @binding(0) var<storage, read> t${this.id}: array<${this.gpuType}>;`,
                    `@group(0) @binding(1) var<storage, read_write> t${out.id}: array<${out.gpuType}>;`,
                    `@compute @workgroup_size(256)`,
                    `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
                    `    let globalIdx = id.x;`,
                    `    if(globalIdx >= ${out.size}) {return;}`,
                    `    let x = t${this.id}[globalIdx];`,
                    `    t${out.id}[globalIdx] = 1.0 / (1.0 + exp(-x));`,
                    `}`)
                let fwd_code = cb.code;
                out._fwd = ()=>{
                    torus.WebGPU.run(fwd_code, [this, out], [workGroups]);
                    return out;
                }
                if(this.allowGrad){
                    cb = new CodeBuilder(
                        `@group(0) @binding(0) var<storage, read> t${out.grad.id}: array<${out.grad.gpuType}>;`,
                        `@group(0) @binding(1) var<storage, read> t${out.id}: array<${out.gpuType}>;`,
                        `@group(0) @binding(2) var<storage, read_write> t${this.grad.id}: array<${this.grad.gpuType}>;`,
                        `@compute @workgroup_size(256)`,
                        `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
                        `    let globalIdx = id.x;`,
                        `    if(globalIdx >= ${out.grad.size}) {return;}`,
                        `    let y = t${out.id}[globalIdx];`,
                        `    t${this.grad.id}[globalIdx] += t${out.grad.id}[globalIdx] * y / (1.0 - y);`,
                        `}`)

                    let back_code = cb.code;
                    out._back = ()=>{
                        torus.WebGPU.run(back_code, [out.grad, out, this.grad], [workGroups]);
                    }
                }

            }
            else{
                out._fwd = ()=>{
                    for (let i = 0; i<this.size; i++){
                        let x = this.data[i];
                        out.data[i] = 1 / (1 + Math.exp(-x));
                    }
                    return out;
                }
                if(this.allowGrad){
                    out._back = ()=>{
                        for (let i = 0; i<this.size; i++){
                            let y = out.data[i];
                            this.grad.data[i] += out.grad.data[i] * y * (1 - y);
                        }
                    }
                }
            }
        }
        return out._fwd();
    }

    torus.prototype.relu = function (...$) {
        $ = Object.assign({k: 0, limit: Infinity}, ...$);
        return torus._element_wise.call(this, {forward: `x => (x>0? (x<${$.limit}? x: (${$.limit} + ${$.k} * (x - ${$.limit}))): x*${$.k})`,
                                 backward_0: `y => (y>0? (y<${$.limit}? 1: ${$.k}): ${$.k})`}, this)._label(`relu(k=${$.k}, limit=${$.limit}): ` + this.shape);
    }

    torus.prototype.softplus = function (...$) {
        $ = Object.assign({beta: 1, threshold: 20}, ...$);
        return torus._element_wise.call(this, {forward: `x => (${$.beta} * x < ${$.threshold})? (Math.log(1 + Math.exp(${$.beta} * x)) / ${$.beta}): x`,
                                 backward_0: `x => (${$.beta} * x < ${$.threshold})? (1 / ( 1 + Math.exp(${-$.beta} * x))): 1`}, this)._label('Softplus: ' + this.shape);
    }

    torus.prototype.silu = function(){
        return this.sigm().mul(this)._label('Silu: ' + this.shape.toString());
    }
}
aggregates:{
    torus.prototype.max = function(dim,  $ = {keepdim: false}){
        let key = dim+$;
        let out = torus.get_out(this, 'max');
        if (!out){
            let di = input.dim_info(dim);
            let stride = di.map(d=>d.size).mul() || this.size;
            let size = this.size / stride;
            out = {
                values: torus.from(new this.dType(size))._src(this)._label(`max-values(dim=${dim})`),
                indices: torus.from(new torus.DEFAULT_TYPE(size))._src(this)._label(`max-indices(dim=${dim})`),
            }
            torus.set_out(this, out, 'max');
            out._fwd = ()=>{
                for (let i = 0, from = 0, to = stride; i < size; i++, from = to, to += stride){
                    let slice = this.data.subarray(from, to);
                    let res = slice.reduce((r, v, i)=>(r.v<v)?{v,i}:r, {v:slice[0], i:0});
                    out.values.data[i] = res.v;
                    out.indices.data[i] = res.i;
                }
                if ($.keepdim){
                    let add = Array(this.dim - 1).fill(1);
                    out.values._shape(add, out.values.shape);
                    out.indices._shape(add, out.indices.shape);
                }
                return out;
            }
            out._back = this.allowGrad? ()=>{
                for(let i = 0, from = 0, to = stride; i < size; i++, from = to, to += stride)
                    this.data.subarray(from, to).fill(out.values.data[i]);
            }: null;
        }
        return out._fwd();
    }
    torus.prototype.min = function(dim,  $ = {keepdim: false}){
        let key = dim+$;
        let out = torus.get_out(this, 'min');
        if(!out){
            let di = input.dim_info(dim);
            let stride = di.map(d=>d.size).mul() || this.size;
            let size = this.size / stride;
            out = {
                values: torus.from(new this.dType(size))._src(this)._label(`min-values(dim=${dim})`),
                indices: torus.from(new torus.DEFAULT_TYPE(size))._src(this)._label(`min-indices(dim=${dim})`),
            }
            torus.set_out(this, out, 'min');
            out._fwd = ()=>{
                for (let i = 0, from = 0, to = stride; i < size; i++, from = to, to += stride){
                    let slice = this.data.subarray(from, to);
                    let res = slice.reduce((r, v, i)=>(r.v>v)?{v,i}:r, {v:slice[0], i:0});
                    out.values.data[i] = res.v;
                    out.indices.data[i] = res.i;
                }
                if ($.keepdim){
                    let add = Array(this.dim - 1).fill(1);
                    out.values._shape(add, out.values.shape);
                    out.indices._shape(add, out.indices.shape);
                }
                return out;
            }
            out._back = this.allowGrad? ()=>{
                for (let i = 0, from = 0, to = stride; i < size; i++, from = to, to += stride)
                    this.data.subarray(from, to).fill(out.values.data[i]);
            }: null;
        }
        return out._fwd();
    }
    torus.prototype.sum = function (dims = [], $ = {keepdim: false}){
        let dims_info = this.dim_info(dims);
        let output = dims_info.map(a=>a.char);
        let input = this.shape_info.map(a=>a.char);
        output = input.filter(a => !output.includes(a));
        let appendix = '';
        if($.keepdim && this.dim !== output.length){
            output = this.shape_info.map(a => dims_info.find(ai=>a.idx === ai.idx)? a.char.toUpperCase(): a.char);
            appendix = ':' + this.shape_info.map(a => dims_info.find(ai=>a.idx === ai.idx)? a.char.toUpperCase()+'=1': '').filter(Boolean).join(',');
        }
        let expr = input.join('') + ' -> ' + output.join('') + appendix;
        return torus.einsum(expr, this)._label(`sum(dims=[${dims}], ${JSON.stringify($)}):\'${expr}\'`);
    }

    torus.prototype.mean = function(dims = [], $ = {keepdim: false}){
        let sum = this.sum(dims, $);
        return  sum.divide(this.size / sum.size)._label('mean');
    }
    torus.prototype.var = function(dims = [], $ = {keepdim: false, correction: 1}){
        let keepdim = $.keepdim;
        $.keepdim = true;
        const mean = this.mean(dims, $);
        // >mean
        $.forward = '(x, y)=>(x - y) ** 2';
        $.backward_0 = '(x, y)=>2 * (x - y)';
        $.backward_1 = '(x, y)=>(-2 * (x - y))';

        let chars = this.gen_chars();
        let ins = chars.join('')
        dims = this.gen_chars(dims);
        let outs;
        if(keepdim){
            chars = chars.map(ch=>{
                if(dims.includes(ch))
                    ch = ch.toUpperCase();
                return ch;
            })
            outs = chars.join('') + ':' + dims.map(d => d.toUpperCase()+'=1').join(',')
        }
        else
            outs = chars.filter(ch=>!dims.includes(ch)).join('');
        let expr = ins + ',' + ins + '->' + outs;



        const sum = torus.einsum(expr, [this, mean]);
        // >sum
        const multiplier = 1/Math.max(0, this.size / mean.size - $.correction);
        const out = sum.mul(multiplier);
        out._label(`var(dims=[${dims}], ${JSON.stringify($)}):\'${expr}\'`);
        return out;
    }
    torus.prototype.std = function(dims = [], $ = {keepdim: false, correction: 1}){
        let out = this.var(...arguments);
        out = out.sqrt();
        out._label(`std(dim = [${dims}], ${JSON.stringify($)})`);
        return out
    }
}
convertors:{
    torus.stack = function (tensors, dim = 0){
        tensors = torus._check_list_of_tensors(tensors);
        let key = 'stack: '+tensors.length+','+dim;
        let out = torus.get_out(this, key);
        if (!out){
            const src_size = torus.compare_shapes_strict(tensors);
            dim = tensors[0].check_dim(dim, true);
            const shape = tensors[0].shape.length? [...tensors[0].shape]: [1];   // Учитываем, что скаляры имеют 'пустую' форму []
            const dType = tensors[0].dType;
            
            const src_count = tensors.length
            shape.splice(dim, 0, src_count);

            out = torus.from(new dType(shape.mul()))._shape(shape)._label(`stack of ${tensors.length} tensors by ${dim} axis`)._src(tensors);
            torus.set_out(this, out, key);
            const step = out.strides[dim];

            out._fwd = (tensors)=>{
                const datas = tensors.map(t=>t.data);
                const out_data = out.data;
                let idx = 0;
                for (let i = 0; i < src_size; i += step){
                    for (let j = 0; j < src_count; j++) {
                        const slice = datas[j].subarray(i, i + step);
                        out_data.set(slice, idx);
                        idx += step;
                    }
                }
                return out._src(tensors);
            }
            if (tensors.some(t=>t.allowGrad)) {
                const split_size = out.shape_info[dim].stride;
                const step = split_size * tensors.length;
                out._back = ()=>{
                    out.src.forEach((tensor, i)=>{
                        if (!tensor.allowGrad)
                            return;
                        let from = split_size * i;
                        for (let p = 0; p < tensor.size; p += split_size){
                            const grad = out.grad.data.subarray(from, from + split_size);
                            for (let j = 0; j<split_size; j++)
                                tensor.grad.data[p + j] += grad[j];
                            from += step;
                        }
                    });
                }
            }
        }
        return out._fwd(tensors);
    }
    torus.prototype.dropout = function(probability = 0.5, inplace = false){
        let out = inplace? this: torus.get_out(this, 'dropout');
        if (!out){
            out = torus.from(this.data.slice(0))._label(`dropout(probability = ${probability}, inplace = ${inplace})`)._src(this)._shape(this);
            torus.set_out(this, out, 'dropout');
            out._fwd = () => {
                let from = this.data;
                let to = out.data;
                let i = this.size;
                while(i--){
                    if (probability && torus.generator()<probability)
                        to[i] = 0;
                    else
                        to[i] = from[i]
                }
                return out;
            }
            out._back = () =>{
                this.grad.data.set(out.grad.data);
            }
        }
        return out._fwd();
    }
    function slice_codegenerator(slicers, to_back = false){
        let space = '   ';
        let shape = [];
        let code = ['let idx=-1;'];
        if (!to_back) {
            code.push('let out_data = out.data;');
            code.push('let data = tensor.data;');
        }
        else {
            code.push('let out_grad = out.grad.data');
            code.push('let grad = tensor.grad.data');
        }
        let dim, start, end, step, add_shape;
        this.strides.forEach((stride, d)=>{
            let slicer = (slicers[d]?.toString() || '').toString().trim();
            dim = this.shape[d];
            if (slicer.length && !Number.isNaN(+slicer)){
                add_shape = false;
                start = +slicer;
                if (start < -dim || start >= dim)
                    throw new Error(`tensor.slice(${slicers}): index ${start} is out of bounds for dimension ${d} with size ${dim}`);
                if (start < 0)
                    start += dim;
                end = start + 1;
                step = 1;
            }
            else{
                add_shape = true;
                slicer = slicer.split(':');
                start = +(slicer[0]?.trim() || 0);
                if (start < 0)
                    start += dim;
                end = +(slicer[1]?.trim() || dim);
                if (end < 0)
                    end += dim;
                step = +(slicer[2]?.trim() || 1);
                // if (step < 0)
                //     step += dim;
                if (step <= 0)
                    throw new Error(`tensor.slice(${slicers}): step for axis ${d} must be greater than zero`);
            }
            if (!Number.isInteger(start) || !Number.isInteger(end) || !Number.isInteger(step))
                throw new Error(`tensor.slice(${slicers}): slice indices and step for axis ${d} must be integers or omitted`);
            if (end > dim)
                end = dim;
            if (start < 0)
                start = 0;
            dim = Math.ceil((end - start) / step);
            if (dim < 0)
                dim = 0;
            let t = space.repeat(d);
            code.push(t + `for(let d${d} = ${start}, _i${d} = ${start * stride}; d${d}<${end}; d${d} += ${step}, _i${d} += ${step * stride}){`)
            if (add_shape)
                shape.push(dim);
        })
        if(!to_back){
            code.push(space.repeat(this.dim)+`out_data[++idx] = data[${this.shape.map((_,i)=>'_i'+i).join(' + ')}];`);
        }
        else{
            code.push(space.repeat(this.dim)+`grad[${this.shape.map((_,i)=>'_i'+i).join(' + ')}] = out_grad[++idx];`);
        }
        this.shape.forEach((_, d)=>{
            code.push(space.repeat(this.dim - d - 1)+`}`);
        })
        let size = shape.mul() || 1;
        if(!to_back){
            if(!shape.length)
                shape = [1];
            code.unshift(`out ??= torus.from(new tensor.dType(${size}))._shape(${shape})._label('slice [${slicers}]');`);
            code.push(`return out;`);
        }
        return code.join('\n');
    }
    torus.prototype.slice = function (...slicers) {
        slicers = torus.flat(slicers);
        if (slicers.length > this.dim)
            throw new Error(`tensor.slice(${slicers}): indexError: too many indices for tensor of dimension ${this.dim}`);
        let out = torus.get_out(this, 'slice');
        if (!out){
            let fn = slice_codegenerator.call(this, slicers);
            fn = new Function('tensor', 'out', fn);
            out = fn(this);
            out._fwd = fn;
            if (this.allowGrad){
                out._src(this);
                let back = slice_codegenerator.call(this, slicers, true);
                back = new Function('tensor', 'out', back);
                out._back = ()=>{
                    back(this, out);
                }
            }
            torus.set_out(this, out, 'slice');
        }
        else
            out._fwd(this, out);
        return out;
    }

    torus.prototype.norm = torus.prototype.normalize = function (k_norm = 1, dim = -1, esp = 1e-5) {
        let key = 'norm: '+ k_norm;
        let out = torus.get_out(this, key);
        if(!out){
            if (!Number.isFinite(k_norm))
                throw new Error(`normalize(): argument k_norm mast be finite, but got ${typeof k_norm === 'number'? k_norm: k_norm?.constructor.name||k_norm}`);
            if (k_norm === 0)
                throw new Error(`normalize(): argument k_norm mast be nonzero`);
            dim = this.dim_info(dim)[0];
            out = torus.from(new this.dType(this.size))._src(this)._shape(this)._label(key);
            torus.set_out(this, out, key);
            let step = dim.stride * dim.size;
            let mults = new Float64Array(this.size / step);
            out._fwd = ()=>{
                for(let i = 0; i<this.size; i += step){
                    let slice = this.data.subarray(i, i + step);
                    let sum = esp;
                    for(let j = 0; j<step; j++){
                        sum += slice[j] ** 2;
                    }
                    sum = (1 / Math.sqrt(sum)) * k_norm;
                    for(let j = 0; j<step; j++){
                        out.data[i + j] = slice[j] * sum;
                    }
                    mults[i]  = sum;
                }
                return out;
            }
            if(this.allowGrad){
                out._back = ()=>{
                    for(let i = 0; i<this.size; i += step){
                        let slice = out.grad.data.subarray(i, i + step);
                        let sum = mults[i];
                        for(let j = 0; j<step; j++){
                            this.grad.data[i + j] += slice[j] * sum;
                        }
                    }
                }
            }
        }
        return out._fwd();
    }

    torus.prototype.view = function (...shape_or_tensor) {
        let key = 'view: '+ shape_or_tensor.toString();
        let out = torus.get_out(this, key);
        if (!out){
            const shape = this.check_shape(shape_or_tensor);
            out = torus.from(this.data)._shape(shape)._src(this)._label('view to ('+shape+')')
            torus.set_out(this, out, key);
            if(this.gpuDataBuffer){
                out.writeToGPU();
                if (this.allowGrad){
                    out._back = ()=>{
                        out.grad.copyGpuBufferTo(this.grad);
                        // out.grad.gpuDataBuffer = this.grad.gpuDataBuffer;
                    };
                }
            }
            else if (this.allowGrad)
                out.grad.data = this.grad.data;
        }
        return out;
    }

    torus.prototype.multinomial = function(num_samples = 1, replacement = false, $ = {}){
        let out = torus.get_out(this, 'multinomial');
        if (!out){
            $ = torus.$({generator: null}, $);
            const step = this.shape.last;
            if(!$.generator)
                $.generator = torus.generator;
            let steps = this.size / step
            out = torus.from(new Uint8Array(steps * num_samples))._shape([steps, num_samples])._label('multinomial')._src(this);
            torus.set_out(this, out, 'multinomial');
            out._fwd = ()=>{
                let idx = -1
                for (let s = 0; s<steps; s++){
                    let sum = 0;
                    let arr = [];
                    let l = s * step;
                    for(let i = 0; i<step; i++){
                        let d = this.data[i+l];
                        sum += Math.abs(d);
                        arr[i] = sum;
                    }
                    for(let n = 0; n<num_samples; n++){
                        let rand = $.generator() * sum;
                        for(let a = 0; a<step; a++){
                            if(arr[a]>=rand){
                                out.data[++idx] = a;
                                break;
                            }
                        }
                    }
                }
                return out;
            }
        }
        return out._fwd();
    }

    torus.prototype.copyGpuBufferTo = function (target, offset = 0){
        let wgs = [Math.min(this.size, 256)];
        let cb = new CodeBuilder(
            `@group(0) @binding(0) var<storage, read> src: array<${this.gpuType}>;`,
            `@group(0) @binding(1) var<storage, read_write> tar: array<${target.gpuType}>;`,
            `@compute @workgroup_size(${wgs})`,
            `fn main(@builtin(global_invocation_id) id: vec3<u32>) {`,
            `   let idx = id.x;`,
            `   if(idx >= ${this.size}) {return;}`,
            `   tar[idx] = src[idx];`,
            `}`
        )
        wgs[0] = Math.ceil(this.size / wgs[0]);
        torus.WebGPU.run(cb.code, [this, target], wgs);
        //
        // const commandEncoder = torus.WebGPU.device.createCommandEncoder();
        // commandEncoder.copyBufferToBuffer(this.gpuDataBuffer, 0, target.gpuDataBuffer, offset,  this.data.byteLength);
        // const commands = commandEncoder.finish();
        // torus.WebGPU.device.queue.submit([commands]);
        // return torus.WebGPU.device.queue.onSubmittedWorkDone();
    }
    torus.join  = function (tensors = []){
        let key = 'join: ' + tensors.map(t => t.shape).flat().join(',');
        let out = torus.get_out(this, key);
        if (!out){
            tensors = torus._check_list_of_tensors(tensors);
            let first = tensors[0];
            const shape = [tensors.length, ...first.shape];
            out = torus.from(new first.dType(shape.mul()))._src(tensors)._label(`join: ${tensors.length} tensors`)._shape(shape);
            torus.set_out(this, out, key);
            if(tensors.some(t=>t.gpuDataBuffer)){
                out.writeToGPU();
                out._fwd = (tensors)=>{
                    let offset = 0;
                    const commandEncoder = torus.WebGPU.device.createCommandEncoder();
                    for(let i = 0; i<tensors.length; i++){
                        let t = tensors[i];
                        commandEncoder.copyBufferToBuffer(t.gpuDataBuffer, 0, out.gpuDataBuffer, offset,  t.data.byteLength);
                        offset += t.data.byteLength;
                    }
                    const commands = commandEncoder.finish();
                    torus.WebGPU.device.queue.submit([commands]);
                    return out._src(tensors);
                }
                out._back = ()=>{
                    let offset = 0;
                    const commandEncoder = torus.WebGPU.device.createCommandEncoder();
                    for(let i = 0; i<tensors.length; i++){
                        let t = tensors[i];
                        if(t.allowGrad && t.gpuDataBuffer){
                            commandEncoder.copyBufferToBuffer(out.grad.gpuDataBuffer, offset, t.grad.gpuDataBuffer, 0, t.data.byteLength);
                        }
                        offset += t.data.byteLength;
                    }
                    const commands = commandEncoder.finish();
                    torus.WebGPU.device.queue.submit([commands]);
                }
            }
            else{
                out._fwd = (tensors)=>{
                    let offset = 0;
                    for(let i = 0; i<tensors.length; i++){
                        let t = tensors[i];
                        out.data.set(t.data, offset);
                        offset += t.size;
                    }
                    return out._src(tensors);
                }

                out._back = ()=>{
                    let offset = 0;
                    for(let i = 0; i<out.src.length; i++){
                        let t = tensors[i];
                        if(t.allowGrad)
                            t.grad.set(out.drad.subarray(offset, t.size));
                        offset += t.size;
                    }
                }
            }
        }
        return out._fwd(tensors);
    }
    torus.cat = torus.concat  = function (tensors = [], dim=-1){
        tensors = torus._check_list_of_tensors(tensors);
        let error_idx = tensors.findIndex(t=> t.dim === 0);   // Нельзя объединять скалярные тензоры, т.к. они не имеют измерений
        if (error_idx !== -1){
            throw new Error(`tensor.concat(): zero-dimensional tensor (at position ${error_idx}) cannot be concatenated`);
        }    
        let out = torus.get_out(this, 'concat');
        if (!out){
            dim = tensors[0].check_dim(dim);
            const shape = torus.compare_shapes_except_dim(tensors, dim);
            const size = shape.mul();
            const dType = torus._common_dType(tensors);
            out = torus.from(new dType(size))._label(`concat ${tensors.length} tensors by ${dim} axis`)._shape(shape)._src(tensors);
            torus.set_out(this, out, 'concat');
            const step = out.size / (out.shape.slice(0, dim).mul() || 1);
            const di = out.dim_info(dim)[0];

            out._fwd = (tensors)=>{
                let from = 0;
                tensors.forEach((tensor)=>{
                    let split_size = di.stride * tensor.shape[dim];
                    let to = 0;
                    for (let p = 0; p < size; p += step){
                        const slice = tensor.data.subarray(to, to + split_size);
                        out.data.set(slice, from + p);
                        to += split_size;
                    }
                    from += split_size;
                });
                out._src(tensors);
                return out;
            }

            if (tensors.some(t=>t.allowGrad)) {
                out._back = ()=>{
                    const shapes = tensors.map(tensor => tensor.shape);
                    const step = shapes.reduce((r, shape) => r + shape.slice(dim).mul(), 0);
                    const split_sizes = shapes.map(shape => shape.slice(dim).mul());
                    tensors.forEach((tensor, i, items)=>{
                        if (!tensor.allowGrad)
                            return;
                        const split_size = split_sizes[i];
                        let from = items.reduce((r, t, j) => j < i? (r + split_sizes[j]): r, 0);
                        for (let p = 0; p < tensor.size; p += split_size){
                            const grad = out.grad.data.subarray(from, from + split_size);
                            for (let j = 0; j<split_size; j++)
                                tensor.grad.data[p + j] += grad[j];
                            from += step;
                        }
                    });
                }
            }
        }
        return out._fwd(tensors);
    }
torus.prototype.split = function(split_size_or_sections, dim = 0){
        let key = `split (${split_size_or_sections.toString() + ', ' + dim})`;
        let outs = torus.get_out(this, key);
        if (!outs){
            let steps = Array.isArray(split_size_or_sections)?split_size_or_sections:[split_size_or_sections];
            dim = this.check_dim(dim);
            const d_info = this.shape_info[dim];
            
            if (steps.length === 1){
                const step = steps[0];
                if (step > d_info.size){
                    steps = [d_info.size];
                }
                else{
                    const  p = Math.floor(d_info.size / step);
                    steps = Array(p).fill(step);
                    const rest = d_info.size - steps.sum();
                    if(rest)
                        steps.push(rest);
                }
            }
            if (steps.sum() !== d_info.size)
                throw new Error(`split_with_sizes expects split_sizes to sum exactly to ${d_info.size} (input tensor's size at dimension ${dim}), but got split_sizes=[${steps.toString()}]`)

            outs = steps.map((s, i) => {
                const shape = this.shape.map((ts,i)=>(i === dim?s:ts));
                return torus.from(new torus.DEFAULT_TYPE(shape.mul()))
                ._src(this)
                ._shape(shape)
                ._label(`splitted part № ${i + 1} by size=${s}`);
            });

            torus.set_out(this, outs, key);

            steps = steps.map(s=>s * d_info.stride);
            outs._fwd = ()=>{
                for (let i = 0; i < outs.length; i++)
                    outs[i].idx = 0;
                let s = 0;
                while (s < this.size){
                    for(let i = 0; i<steps.length; i++){
                        const out = outs[i];
                        const step = steps[i];
                        const end = s + step ;
                        out.data.set(this.data.subarray(s, end), out.idx);
                        out.idx += step;
                        s = end;
                    }
                }
                return outs;
            }
            if (this.allowGrad){
                const full_step = steps.sum();
                outs.map((out, o)=>{
                    const step = steps[o];
                    const start = steps.reduce((r, s, i) => r + (i<o? s: 0), 0);
                    out._back = ()=>{
                        let from = start;
                        for (let i = 0; i < out.size; i+=step){
                            this.grad.data.set(out.grad.data.subarray(i, i+step), from);
                            from += full_step;
                        }
                    }
                });
            }
        }
        return outs._fwd();
    }
    torus.prototype.float = function (type = torus.DEFAULT_TYPE){
        if (this.dType !== type){
            let data = new type(this.size);
            data.set(this.data, 0);
            // this.destroy(false);
            this.data = data;
        }
        return this;
    }

    torus.prototype.tril = function (diagonal = 0) {
        if (this.dim < 2)
            throw new Error('input tensor must have at least 2 dimensions');
        const _x = this.shape[this.dim - 1];
        const _y = this.shape[this.dim - 2];
        const step = _x * _y;
        const _z = this.size/step;
        const data = new this.dType(this.size);
        //Смещение диагонали не должно выходить за пределы матрицы
        diagonal = Math.max(Math.min(diagonal, _x-1), -_y);
        //В начале могут идти строки заполненные нулями
        const start_diagonal = -Math.min(0, diagonal);
        //Последние строки матрицы могут быть скопированы целиком. Находим индекс начала таких строк.
        const stop_diagonal = Math.min(_y, _x - 1 - diagonal);
        const length_copy = (_y - stop_diagonal) * _x;   //Общая длина копируемых строк матрицы
        diagonal++;   //Увеличение на 1 упрощает последующие расчёты
        let bias = 0;
        if (_y-start_diagonal<60 || _x<60)    //В маленькие треугольники дешевле записывать поэлементно
            for (let z=0; z<_z; z++) {
                bias += start_diagonal * _x;
                for (let y=start_diagonal; y<stop_diagonal; y++) {   //Треугольная часть данных в матрице
                    let x = bias + y + diagonal;
                    while(x-- > bias)
                        data[x] = this.data[x];
                    bias += _x;
                }
                data.set( this.data.subarray(bias, bias + length_copy), bias );   //Копируем полные строки
                bias += length_copy;
            }
        else    //В большие треугольники дешевле записывать элементы блоками
            for (let z=0; z<_z; z++) {
                bias += start_diagonal * _x;
                for (let y=start_diagonal; y<stop_diagonal; y++) {   //Треугольная часть данных в матрице
                    data.set( this.data.subarray(bias, bias + y + diagonal), bias );
                    bias += _x;
                }
                data.set( this.data.subarray(bias, bias + length_copy), bias );   //Копируем полные строки
                bias += length_copy;
            }
        const out = torus.from(data)._shape(this)._label('tril');
        out._back = () => {
            this.grad.data = this.grad.data.map((g, i)=>{
                return g + out.grad.data[i];
            })
        }
        return out;
    }
}
torus.prototype.transpose = function(dim0 = -1, dim1 = -2) {
    if (this.dim < 2)
        throw new Error(`Dimension out of range (expected more 2 or more, but got ${this.dim})`);
    dim0 = this.dim_info(dim0)[0].idx;
    dim1 = this.dim_info(dim1)[0].idx;
    if (dim0 === dim1)
        throw new Error(`transpose: dim0 and dim1 can not be equal`);

    let var_in = this.shape_info.map(v=>v.char);

    const var_out = var_in.map((v, i)=>{
        if (i === dim0)
            return var_in[dim1]
        if (i === dim1)
            return var_in[dim0]
        return v.char;
    })
    const expression = var_in.join('')+'->'+var_out.join('');
    return torus.einsum(expression, this)._label(`transpose (${dim0}, ${dim1}) ` + expression);
}
torus.prototype.pad = function(paddings, mode = 'constant', constant_value = 0) {
    let new_shape = this.shape.slice();
    for (let i = 0; i < paddings.length; i++) {
        new_shape[i] += paddings[i] * 2;
    }
    let new_data = new this.dType(new_shape.mul()).fill(constant_value);
    let offsets = paddings.slice();
    let strides = [1];
    for (let i = this.dim - 1; i >= 0; i--) {
        strides[i] = strides[i + 1] * this.shape[i];
    }
    let index = (indices) => {
        let offset = 0;
        for (let i = 0; i < indices.length; i++) {
            offset += indices[i] * strides[i];
        }
        return offset;
    }
    let unpadded_indices = (indices) => {
        let result = [];
        for (let i = 0; i < indices.length; i++) {
            result.push(Math.max(Math.min(indices[i] - offsets[i], this.shape[i]), 0));
        }
        return result;
    }
    let padded_indices = (indices) => {
        let result = [];
        for (let i = 0; i < indices.length; i++) {
            result.push(Math.max(Math.min(indices[i], new_shape[i]), 0));
        }
        return result;
    }
    let i = this.data.length;
    while(i--){
        let indices = unpadded_indices(index(i));
        new_data[index(indices)] = this.data[i];
    }
    if (mode === 'reflect') {
        for (let i = 0; i < paddings.length; i++) {
            let axis_size = this.shape[i];
            let padding_size = paddings[i];
            for (let j = 0; j < axis_size; j++) {
                let left_index = index([...Array(i).fill(0), j, ...Array(this.dim - i - 1).fill(0)]);
                let right_index = index([...Array(i).fill(0), axis_size - 1 - j, ...Array(this.dim - i - 1).fill(0)]);
                let left_offset = Math.floor((left_index - padding_size) / (axis_size + padding_size * 2));
                let right_offset = Math.floor((right_index - padding_size) / (axis_size + padding_size * 2));
                for (let k = 1; k <= padding_size; k++) {
                    let left_padded_index = index([...Array(i).fill(0), padding_size - k + left_offset * (axis_size + padding_size * 2), ...Array(this.dim - i - 1).fill(0)]);
                    let right_padded_index = index([...Array(i).fill(0), padding_size - k + right_offset * (axis_size + padding_size * 2), ...Array(this.dim - i - 1).fill(0)]);
                    new_data[left_padded_index] = this.data[left_index];
                    new_data[right_padded_index] = this.data[right_index];
                }
            }
        }
    } else if (mode === 'replicate') {
        for (let i = 0; i < paddings.length; i++) {
            let axis_size = this.shape[i];
            let padding_size = paddings[i];
            for (let j = 0; j < axis_size; j++) {
                let left_index = index([...Array(i).fill(0), j, ...Array(this.dim - i - 1).fill(0)]);
                let right_index = index([...Array(i).fill(0), axis_size - 1 - j, ...Array(this.dim - i - 1).fill(0)]);
                for (let k = 1; k <= padding_size; k++) {
                    let left_padded_index = index([...Array(i).fill(0), padding_size - k, ...Array(this.dim - i - 1).fill(0)]);
                    let right_padded_index = index([...Array(i).fill(0), new_shape[i] - padding_size + k, ...Array(this.dim - i - 1).fill(0)]);
                    new_data[left_padded_index] = this.data[left_index];
                    new_data[right_padded_index] = this.data[right_index];
                }
            }
        }
    } else if (mode === 'circular') {
        for (let i = 0; i < paddings.length; i++) {
            let axis_size = this.shape[i];
            let padding_size = paddings[i];
            for (let j = 0; j < axis_size; j++) {
                let left_index = index([...Array(i).fill(0), j, ...Array(this.dim - i - 1).fill(0)]);
                let right_index = index([...Array(i).fill(0), axis_size - 1 - j, ...Array(this.dim - i - 1).fill(0)]);
                for (let k = 1; k <= padding_size; k++) {
                    let left_padded_index = index([...Array(i).fill(0), padding_size - k, ...Array(this.dim - i - 1).fill(0)]);
                    let right_padded_index = index([...Array(i).fill(0), new_shape[i] - padding_size + k, ...Array(this.dim - i - 1).fill(0)]);
                    new_data[left_padded_index] = this.data[index([...Array(i).fill(0), (left_index + k) % axis_size, ...Array(this.dim - i - 1).fill(0)])];
                    new_data[right_padded_index] = this.data[index([...Array(i).fill(0), (right_index + k) % axis_size, ...Array(this.dim - i - 1).fill(0)])];
                }
            }
        }
    }
    let result = new tensor(new_data, this.dType);
    result._shape(new_shape);
    result._label(`pad(${paddings}, ${mode}, ${constant_value})`);
    result._src(this);
    result._back = () => {
        let unpadded_grad = new this.dType(this.data.length);
        let i = this.data.length
        while (i--) {
            let indices = unpadded_indices(index(i));
            unpadded_grad[i] = result.grad.data[index(indices)];
        }
        this.grad.data = unpadded_grad;
    }
    return result;
}

globalThis.range ??= (count = 0)=>{
    count = Math.floor(count)
    return Array(count).fill().map((_, i)=>i);
}

torus.context2static = ()=>{
    const descr = Object.getOwnPropertyDescriptors(torus.prototype);
    for (let n in descr){
        if (n.startsWith('_') || n.endsWith('_')) continue;
        const d = descr[n];
        if (!d.enumerable)
            continue;
        if (d.value?.constructor !== Function)
            continue;
        torus[n] ??= (tensor, ...params)=>{
            return d.value.call(tensor, ...params);
        }
    }
}

torus.context2static();
torus.label_from_error = (deep = 0)=>{
    return new Error().stack.split('at torus.')?.[1 + deep]?.split(' ')?.[0] || 'torus';
}