<!DOCTYPE html>
<html>
<head>
    <title>Test Cursor Scroll Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-info {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
        }
        .code-block {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Тест исправления прокрутки курсора в Jupyter</h1>
    
    <div class="test-info">
        <h2>Описание проблемы</h2>
        <p>Функция <code>on_change_cursor</code> в Jupyter редакторе имела проблему с прокруткой при быстром перемещении курсора (зажатой клавише). Прокрутка "застревала" из-за использования <code>debounce</code>, который отменял предыдущие вызовы.</p>
    </div>

    <div class="instructions">
        <h2>Что было исправлено</h2>
        <ol>
            <li><strong>Заменен debounce на throttle</strong> - теперь прокрутка выполняется регулярно, а не отменяется</li>
            <li><strong>Добавлена частота 60fps</strong> - используется задержка 16ms для плавной прокрутки</li>
            <li><strong>Улучшена логика проверки видимости</strong> - более точное определение необходимости прокрутки</li>
            <li><strong>Разделены ключи throttle</strong> - отдельные ключи для движения вверх и вниз</li>
        </ol>
    </div>

    <div class="code-block">
        <h3>Основные изменения:</h3>
        <pre>
// Было:
this.debounce('jupyter.scrollTop', ()=>{
    this.jupyter.scrollTop = move;
})

// Стало:
this.throttle('jupyter.scrollTop.up', ()=>{
    this.jupyter.scrollTop = move;
}, 16) // ~60fps для плавности
        </pre>
    </div>

    <div class="instructions">
        <h2>Как тестировать</h2>
        <ol>
            <li>Откройте Jupyter notebook с большим количеством строк кода</li>
            <li>Поместите курсор в середину кода</li>
            <li>Зажмите клавишу "Стрелка вверх" или "Стрелка вниз"</li>
            <li>Убедитесь, что прокрутка работает плавно и не застревает</li>
            <li>Проверьте, что видимая строка соответствует позиции курсора</li>
        </ol>
    </div>

    <div class="test-info">
        <h2>Технические детали</h2>
        <ul>
            <li><strong>throttle vs debounce</strong>: throttle выполняет функцию регулярно, debounce откладывает выполнение</li>
            <li><strong>Частота 16ms</strong>: соответствует 60 FPS для плавной анимации</li>
            <li><strong>Разные ключи</strong>: 'jupyter.scrollTop.up' и 'jupyter.scrollTop.down' позволяют независимую обработку</li>
            <li><strong>Улучшенная проверка</strong>: более точное определение границ видимости строк</li>
        </ul>
    </div>

    <script>
        console.log('Тест загружен. Проверьте Jupyter notebook для тестирования исправлений.');
    </script>
</body>
</html>
