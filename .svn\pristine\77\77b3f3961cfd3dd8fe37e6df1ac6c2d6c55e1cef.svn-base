class WebGpu{
    adapter = null;
    device = null;
    buffers = new Map();
    readbackBuffer = null;
    constructor(){
        return new Promise(async resolve=>{
            this.adapter = await navigator.gpu.requestAdapter();
            let maxBufferSize = this.info.maxStorageBufferBindingSize;
            const requiredLimits = {
                maxStorageBufferBindingSize : maxBufferSize,
                maxBufferSize};
            this.device = await this.adapter.requestDevice({requiredLimits});
            resolve(this);
        })
    }
    get info(){
        let d_info = Object.getOwnPropertyDescriptors(this.adapter.info.__proto__);
        let info = Object.create(null);
        for(let key in d_info){
            info[key] = this.adapter.info[key];
            if(key === 'memoryHeaps'){
                info[key] = this.adapter.info[key].map(m=>({properties: m.properties, size:m.size}))
            }
        }
        let d_limits = Object.getOwnPropertyDescriptors(this.adapter.limits.__proto__);
        let limits = Object.create(null);
        for(let key in d_limits){
            limits[key] = this.adapter.limits[key];
        }
        return {
            info,
            limits,
        }
    }
    destroy(bufferArray){
        this.buffers.delete(bufferArray);
    }
    writeData(bufferArray, copy = false, label = 'buffer'){ // data - BufferArray
        let buffer = this.buffers.get(bufferArray);
        if (!buffer) {
            buffer = this.device.createBuffer({
                size: bufferArray.byteLength,
                usage: GPUBufferUsage.STORAGE | GPUBufferUsage.COPY_DST | GPUBufferUsage.COPY_SRC,
                label,
                mappedAtCreation: false
            });
            this.buffers.set(bufferArray, buffer);
            this.device.queue.writeBuffer(buffer, 0, bufferArray);
            return buffer;
        }
        if (copy)
            this.device.queue.writeBuffer(buffer, 0, bufferArray);
        return buffer;
    }
    async readData(bufferArray){
        let buffer = this.buffers.get(bufferArray);
        if (!buffer)
            throw new Error('Readable buffer not found.')
        if (this.readbackBuffer?.size !== buffer.size){
            this.readbackBuffer?.destroy();
            this.readbackBuffer  = null;
        }
        if (!this.readbackBuffer){
            this.readbackBuffer = this.device.createBuffer({
                size: buffer.size,
                usage: GPUBufferUsage.COPY_DST | GPUBufferUsage.MAP_READ
            });
        }
        const commandEncoder = this.device.createCommandEncoder();
        commandEncoder.copyBufferToBuffer(
            buffer,  // источник
            0,       // смещение в источнике
            this.readbackBuffer,  // назначение
            0,       // смещение в назначении
            buffer.size     // размер данных
        );

        // 3. Отправляем команды
        this.device.queue.submit([commandEncoder.finish()]);

        // 4. Асинхронно запрашиваем данные
        await this.readbackBuffer.mapAsync(GPUMapMode.READ);

        // 5. Получаем данные
        let data = this.readbackBuffer.getMappedRange().slice(0);
        this.readbackBuffer.unmap();
        data = new bufferArray.constructor(data);
        bufferArray.set(data);
        return bufferArray;
    }
    clearBuffer(buffer){
        const commandEncoder = this.device.createCommandEncoder();
        commandEncoder.clearBuffer(buffer);
        this.device.queue.submit([commandEncoder.finish()])
    }
    run(code, buffers = [], workgroups = [8,8,1]){
        const computePipeline = this.device.createComputePipeline({
            layout: "auto",
            compute: {
                module: this.device.createShaderModule({ code }),
                entryPoint: "main",
            },
        });
        const entries = buffers.map((buffer, i)=>{
            buffer = buffer.gpuDataBuffer || buffer;
            return {binding: i, resource: { buffer }}
        });
        const bindGroup = this.device.createBindGroup({
            layout: computePipeline.getBindGroupLayout(0),
            entries
        });

        const commandEncoder = this.device.createCommandEncoder();
        const passEncoder = commandEncoder.beginComputePass();
        passEncoder.setPipeline(computePipeline);
        passEncoder.setBindGroup(0, bindGroup);
        passEncoder.dispatchWorkgroups(...workgroups);
        passEncoder.end();
        this.device.queue.submit([commandEncoder.finish()]);
        return commandEncoder;
    }
}
const webgpu = await (new WebGpu());
export {
    WebGpu,
    webgpu
};
