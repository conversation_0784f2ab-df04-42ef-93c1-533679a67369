import * as http from "node:http";
import * as fs from "node:fs";
import * as mime from "mime-types";
import * as crypto from "node:crypto";
import * as zlib from "node:zlib";
import { pipeline, Readable } from 'node:stream';
import * as Worker from "worker_threads";

import {Reactor} from './oda/reactor.js';
import { $host, $structure, $folder, $file, $storage, $item } from './server.js';
import * as WebSocket from "ws";


import { register } from 'node:module';

register('./loader.mjs', {
    parentURL: import.meta.url,
    data: {
        ssid: globalThis.SSID
    },
});


const hash = crypto.createHash('sha256');
const hostname = '127.0.0.1';
const port = 8001;
const ws_port = 9001;

function parseCookies(request) {
    const list = {};
    const cookieHeader = request.headers?.cookie;
    if (!cookieHeader) return list;

    cookieHeader.split(`;`).forEach(function (cookie) {
        let [name, ...rest] = cookie.split(`=`);
        name = name?.trim();
        if (!name) return;
        const value = rest.join(`=`).trim();
        if (!value) return;
        list[name] = decodeURIComponent(value);
    });
    return list;
}

const server = http.createServer(async (request, response) => {
    let item;
    try {
        const cookies = parseCookies(request);
        let user = $odb.get_user(cookies.sessionid);
        const host = 'http://odb.net'; //request.headers.host;
        const url = new URL(host + request.url);
        let path = decodeURI(url.pathname);
        item = await ODB.host.get_item(path, user);
        if (!item)
            throw new Error(`item${path.includes('*') ? 's' : ''} "${path}" not found`);
        user.events[path] = Date.now();
        let method;
        const search = decodeURI(url.search).slice(1);
        let params = search.split('&').reduce((res, step) => {
            const idx = step.indexOf('=');
            if (idx < 0)
                method ??= step;
            else
                res[step.substring(0, idx)] = step.substring(idx + 1);
            return res;
        }, {});
        let result;
        if (item instanceof $item) {
            if (item.isFolder && !method && path.slice(-1) === '/') { // redirect folder to index.html
                response.writeHead(302, { Location: path + 'index.html' });
                response.end();
                return;
            }
            // else if (item.isFile && path !== item.path) { // redirect file to real path
            //     response.writeHead(302, { Location: item.path });
            //     response.end();
            //     return;
            // }
            result = exec_item_method(item, method, params, request)
        }
        else if (Array.isArray(item)) {
            let items = await Promise.all(item);
            if (!method)
                result = items;
            else {
                result = items.map(item => exec_item_method(item, method, params, request) || item);
                result = await Promise.all(result);
            }
        }
        else
            result = item;

        if (result?.then)
            result = await result;
        const header = { "Access-Control-Allow-Origin": "*", "mode": 'no-cors', "Content-Type": "application/json" };
        if (item.isFile) {
            if (method === 'download') {
                header["Content-Type"] = "application/octet-stream";
                header["Content-Disposition"] = "attachment; filename=" + item.id;
            }
            else if (!method || method === 'load') {
                const onError = (err) => {
                    if (err) {
                        // If an error occurs, there's not much we can do because
                        // the server has already sent the 200 response code and
                        // some amount of data has already been sent to the client.
                        // The best we can do is terminate the response immediately
                        // and log the error.
                        response.end();
                        console.error('An error occurred:', err);
                    }
                };
                header["Content-Type"] = mime.contentType(item.id);

                // header["Cache-Control"] = "must-revalidate, public, max-age=3600";

                let acceptEncoding = request.headers['accept-encoding'];
                if(acceptEncoding){

                    if (acceptEncoding.match(/\bdeflate\b/)) {
                        header["Content-Encoding"] = 'deflate'
                        pipeline(Readable.from(result), zlib.createDeflate(), response, onError);
                        
                    } 
                    else if (acceptEncoding?.match(/\bgzip\b/)) {
                        header["Content-Encoding"] = 'gzip'
                        pipeline(Readable.from(result), zlib.createGzip(), response, onError);
      
                    }
                    else if (acceptEncoding?.match(/\br\b/)) {
                        header["Content-Encoding"] = 'br'
                        pipeline(Readable.from(result), zlib.createBrotliCompress(), response, onError);

                    }
                    result = null;
                }

                // 
            }
            else {
                result = JSON.stringify(result, null, +params.space || 4);
            }
            header.Location = url.origin + '/' + item.path;
        }
        else if (typeof result === 'object') {
            if (result?.constructor.name === 'bound Reactor')
                result = result[REACTOR].CACHE.data;
            result = JSON.stringify(result, null, +params.space || 4);
        }
        else {
            header["Content-Type"] = "text/html";
            result = result?.toString?.();
        }
        if (!user.sessionid) {
            user.sessionid = ODB.genGUID();
            header['Set-Cookie'] = `sessionid=${user.sessionid};Path=/`;
            $odb.users[user.sessionid] = user;
        }

        response.writeHead(200, header);
        if (result)
            response.end(result);
    }
    catch (e) {
        response.writeHead(400, {
            "Content-Type": "text/html",
            "mode": 'no-cors',
            "Access-Control-Allow-Origin": "*"
        });
        let text = e.stack.split('\n');
        let label = item?.toJSON?('<h3>' + item.id + ' (' + item.ctor + ')' + '<br>' + item.path + '<br></h3>'):'';
        text = `<h4 style="font-family: monospace;">ODB SERVER ERROR: ${label + text[0]}</h4>`
        response.end(text);
    }

});

function exec_item_method(item, method, params, request) {
    let result = item;
    if (item instanceof $item) {
        method ||= item[request.method]
        if (method) {
            const run_method = async () => {
                let handler; // todo раскомментарить сначала внешние хендлеры, затем собтсвенные методы
                // const handlers = await item.handlers;
                // handler = handlers.find?.(i => i.id === method)
                // if (handler)
                //     return handler.execute(params);
                if (!(method in item)) {
                    let prop;
                    let t = item;
                    while (t && !prop) {
                        prop = Object.getOwnPropertyDescriptor(t, method);
                        t = t.__proto__;
                    }
                    if (prop) {
                        if (prop.value) {
                            if (typeof prop.value === 'function')
                                return prop.value.call(item, params, request.post);
                            return prop.value;
                        }
                        else if (prop.set && request.post)
                            return prop.set.call(item, request.post);
                    }
                    else
                        throw new Error(`Unknown method "${method}" for:<br>${item}`)
                }
                handler = item[method];
                if (typeof handler === 'function')
                    return handler.call(item, params);
                return handler;
            }
            return run_method();
        }
    }
    return result;
}
server.listen({ port }, () => {
    console.log(`Server running at http://${hostname}:${port}/`);
});
const wsServer = new WebSocket.WebSocketServer({ port: ws_port });
function onConnect(ws, request) {
    const cookies = parseCookies(request);
    let user = $odb.get_user(cookies.sessionid);
    user.webSocket = ws;
    console.log('User connected');
    ws.send(`hello ${user.label || 'GUEST'}!`);
    ws.on('message', (message, isBinary) => {
        let str = new TextDecoder('utf-8').decode(message);
        // console.log(ws, str);
    })
    ws.on('close', () => {
        console.log('User disconnected');
    })
}

wsServer.on('connection', onConnect);
globalThis.$odb = class $odb extends $structure {
    id = 'odb';
    label = 'ODB';
    prefix = '';
    path = ''
    hostMap = new Map();
    host;
    get folder(){
        return './core/';
    }
    get dir(){
        return './';
    }
    get items() {
        return [this.host];
    }
    async find_host(id) {
        return $host.build({ id }, this);
    }
    set host(n) {
        this.data.host = n;
    }

    genGUID(size = 15) {
        let time = new Date().getTime();
        if (time !== ODB.time) {
            ODB.time = time;
            ODB.rndID = [];
        }
        time = time.toString(16);
        size -= time.length;

        let rnd = '';
        for (let i = 0; i < size; i++) {
            rnd += Math.floor(Math.random() * 16).toString(16);
        }
        if (ODB.rndID.indexOf(rnd) > -1)
            return ODB.genGUID();
        ODB.rndID.push(rnd)
        return (rnd + time);
    }
    // get files() {
    //     return this.$dir.files;
    // }
    get indexHTML() {
        let file = this.files.find(f => f.id === 'index.html');
        return file.load({encoding: 'utf-8'});
    }
    static __items__ = Object.create(null);
    
    static users = Object.create(null);
    static get_user(session_id) {
        return this.users[session_id] ??= { events: {}, roles: [] }
    }
}
function restoreStructure(item, path = '.') {
    path += '/' + (item.id || 'database');
    let file_path = path + '/' + item.ctor + '/';
    fs.mkdirSync(file_path, { recursive: true });
    file_path += item.ctor.slice(1) + '.js';
    let items = item.items || [];
    delete item.items;
    if (!fs.existsSync(file_path)) {
        item.id ??= ODB.genGUID();
        item.create_time = new Date().toISOString();
        item.KEY = crypto.createHash('sha512').update(JSON.stringify(item)).digest('hex');
        fs.writeFileSync(file_path, 'export default ' + item.serialize(3));
    }
    for (let i of items) {
        restoreStructure(i, path);
    }
}
globalThis.ODB = new $odb();

let data = (await import('./dna.js')).default;
restoreStructure(data);
ODB.ext_folders = {};

let pack = fs.readFileSync('./package.json', {encoding: "utf-8"});
pack = JSON.parse(pack)
for (let key in pack.ext_folders || {}) {
    try {
        let dir = pack.ext_folders[key];
        data = fs.statSync(dir);
        data.id = '$' + key;
        let folder = ODB.ext_folders['$' + key] = new $folder(data);
        folder.folder = dir;
    }
    catch (e) {
        console.warn('ODB: link ext_folders...', e);
    }
}

ODB.host = await $host.build('host', ODB);
// const file = await ODB.host.$folder.getFile('host.js');
// ODB.host.data = await file.script;


globalThis.ODA = function (prototype) {}

console.log('ODB is running:', ODB.host.step);
console.log('to launch: http://127.0.0.1:8001/*/~/page/navigator/index.html');
 
//      http://127.0.0.1:8001?deep=3
//      http://127.0.0.1:8001/b:sandbox/c:a7ce193fe1da591
//      http://127.0.0.1:8001/b:sandbox/~/page/navigator/index.html
//      http://127.0.0.1:8001/b:sandbox/~/form/index.html
//      http://127.0.0.1:8001/b:sandbox/~/form.js

//      http://127.0.0.1:8001/b:sandbox/~/form.js/~/code-editor/index.html



//      http://127.0.0.1:8001/$oda/icon/index.html
//      http://127.0.0.1:8001/$oda/tools/icons/icons-test.html
//      http://localhost:8001/$oda/tools/icons/icons-set/index.html
//      http://localhost:8001/$oda/tools/icons/icons-tree/index.html
//      http://127.0.0.1:8001/$oda/checkbox/index.html


//      http://127.0.0.1:8001/$oda/benchmarks/dbmon/index.html
//      http://127.0.0.1:8001/$oda/benchmarks/sierpinski/index.html
//      http://127.0.0.1:8001/$oda/apps/bugs/index.html
//      http://localhost:8001/$oda/button/index.html

//      http://localhost:8001/$oda/layouts/app-layout/index.html
//      http://localhost:8001/$oda/layouts/tabs/index.html

//      http://localhost:8001/$oda/layouts/splitter/index.html

//      http://localhost:8001/$oda/layouts/hexagon-layout/index.html
//      http://localhost:8001/$oda/editors/code-editor/index.html


//      http://localhost:8001/$oda/editors/markdown/index.html
//      http://localhost:8001/$oda/markdown-editor/index.html
//      http://localhost:8001/$oda/editors/markdown-viewer/index.html

//      http://localhost:8001/$oda/editors/jspreadsheet-editor/index.html

//      http://127.0.0.1:8001/$oda/tools/jupyter/index.html
//      http://127.0.0.1:8001/$oda/tools/diff/index.html
//      http://127.0.0.1:8001/$oda/tools/merge/index.html

//      http://localhost:8001/$oda/ruler-grid/index.html
//      http://localhost:8001/$oda/scheme-layout/index.html

//      http://localhost:8001/$oda/menu/index.html
//      http://localhost:8001/$oda/table/index.html
//      http://localhost:8001/$oda/tools/property-grid/index.html


//      ODB

//      http://localhost:8001/b:sandbox/~/lib/item-icon/index.html
//      http://localhost:8001/~/lib/item-node/index.html
//      http://localhost:8001/~/lib/item-toolbar/index.html
//      http://localhost:8001/~/lib/item-tree/index.html

//      http://localhost:8001/c:maps/data/000000000000001.js

// Object
//      http://localhost:8001/~/form/index.html


//  AGregAI